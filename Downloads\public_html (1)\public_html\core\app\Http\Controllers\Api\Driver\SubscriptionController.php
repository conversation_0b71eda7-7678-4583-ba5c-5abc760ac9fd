<?php

namespace App\Http\Controllers\Api\Driver;

use App\Constants\Status;
use App\Http\Controllers\Controller;
use App\Models\DriverSubscription;
use App\Models\GeneralSetting;
use App\Models\Transaction;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SubscriptionController extends Controller
{
    public function getSubscriptionInfo()
    {
        $driver = Auth::user();
        $general = gs();
        
        // Check if we're using subscription model
        if ($general->revenue_model != Status::REVENUE_SUBSCRIPTION) {
            $notify[] = "Subscription model is not active";
            return apiResponse("error", "error", $notify);
        }
        
        $activeSubscription = $driver->activeSubscription;
        
        $data = [
            'subscription_fee' => showAmount($general->driver_subscription_fee),
            'has_active_subscription' => $driver->hasActiveSubscription(),
            'driver_balance' => showAmount($driver->balance),
            'subscription' => $activeSubscription ? [
                'id' => $activeSubscription->id,
                'start_date' => showDateTime($activeSubscription->start_date),
                'end_date' => showDateTime($activeSubscription->end_date),
                'status' => $activeSubscription->status,
                'amount' => showAmount($activeSubscription->amount)
            ] : null
        ];
        
        $notify[] = "Subscription information";
        return apiResponse("success", "success", $notify, $data);
    }
    
    public function paySubscription(Request $request)
    {
        $driver = Auth::user();
        $general = gs();
        
        // Check if we're using subscription model
        if ($general->revenue_model != Status::REVENUE_SUBSCRIPTION) {
            $notify[] = "Subscription model is not active";
            return apiResponse("error", "error", $notify);
        }
        
        // Check if driver already has an active subscription
        if ($driver->hasActiveSubscription()) {
            $notify[] = "You already have an active subscription";
            return apiResponse("error", "error", $notify);
        }
        
        // Get subscription fee
        $subscriptionFee = $general->driver_subscription_fee;
        
        // Check if driver has sufficient balance
        if ($driver->balance < $subscriptionFee) {
            $notify[] = "Insufficient balance. Please deposit funds to subscribe.";
            return apiResponse("insufficient_balance", "error", $notify, [
                'required_amount' => showAmount($subscriptionFee),
                'current_balance' => showAmount($driver->balance),
                'deficit' => showAmount($subscriptionFee - $driver->balance)
            ]);
        }
        
        // Deduct subscription fee from driver's balance
        $driver->balance -= $subscriptionFee;
        $driver->save();
        
        // Create transaction record
        $transaction = new Transaction();
        $transaction->driver_id = $driver->id;
        $transaction->amount = $subscriptionFee;
        $transaction->post_balance = $driver->balance;
        $transaction->charge = 0;
        $transaction->trx_type = '-';
        $transaction->trx = getTrx();
        $transaction->remark = 'subscription_payment';
        $transaction->details = 'Weekly subscription payment';
        $transaction->save();
        
        // Create subscription record
        $startDate = Carbon::now();
        $endDate = Carbon::now()->addDays(7);
        
        $subscription = new DriverSubscription();
        $subscription->driver_id = $driver->id;
        $subscription->amount = $subscriptionFee;
        $subscription->start_date = $startDate;
        $subscription->end_date = $endDate;
        $subscription->status = DriverSubscription::STATUS_ACTIVE;
        $subscription->save();
        
        // Send notification to driver
        $driver->notify('DRIVER_SUBSCRIPTION_PAYMENT', [
            'amount' => showAmount($subscriptionFee),
            'end_date' => showDateTime($endDate)
        ]);
        
        $notify[] = "Subscription payment successful";
        return apiResponse("success", "success", $notify, [
            'subscription' => [
                'id' => $subscription->id,
                'start_date' => showDateTime($subscription->start_date),
                'end_date' => showDateTime($subscription->end_date),
                'status' => $subscription->status,
                'amount' => showAmount($subscription->amount)
            ],
            'driver_balance' => showAmount($driver->balance)
        ]);
    }
}
