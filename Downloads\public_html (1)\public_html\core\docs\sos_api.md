# SOS Alert API Documentation

This documentation describes the SOS (emergency) alert endpoints available for passengers during a ride.

## Endpoints

### 1. Regular SOS Alert

Send an SOS alert with a custom message.

**Endpoint:** `POST /api/ride/sos/{ride_id}`

**Headers:**
```
Authorization: Bearer {your_access_token}
Content-Type: application/json
```

**Parameters:**
- `ride_id` (path parameter, required) - The ID of the active ride
- `latitude` (required) - Current latitude of the passenger
- `longitude` (required) - Current longitude of the passenger
- `message` (optional) - Custom emergency message

**Example Request:**
```json
{
    "latitude": 12.3456,
    "longitude": 78.9012,
    "message": "Driver is driving recklessly"
}
```

**Success Response:**
```json
{
    "remark": "success",
    "status": "success",
    "message": ["SOS request successfully"],
    "data": null
}
```

### 2. Quick SOS Alert

Send an immediate SOS alert without requiring a message. This is designed for situations where speed is critical.

**Endpoint:** `POST /api/ride/quick-sos/{ride_id}`

**Headers:**
```
Authorization: Bearer {your_access_token}
Content-Type: application/json
```

**Parameters:**
- `ride_id` (path parameter, required) - The ID of the active ride
- `latitude` (required) - Current latitude of the passenger
- `longitude` (required) - Current longitude of the passenger

**Example Request:**
```json
{
    "latitude": 12.3456,
    "longitude": 78.9012
}
```

**Success Response:**
```json
{
    "remark": "success",
    "status": "success",
    "message": ["Emergency alert sent successfully"],
    "data": null
}
```

## Error Responses

Both endpoints may return the following errors:

### Validation Error
```json
{
    "remark": "validation_error",
    "status": "error",
    "message": ["The latitude field is required", "The longitude field is required"],
    "data": null
}
```

### Invalid Ride Error
```json
{
    "remark": "invalid_ride",
    "status": "error",
    "message": ["The ride is not found"],
    "data": null
}
```

## Important Notes

1. Both endpoints are only available for active/running rides
2. The quick SOS endpoint automatically sets a default "Emergency Alert" message
3. The quick SOS endpoint marks the notification as urgent for faster admin response
4. Both endpoints will record the passenger's current location for emergency response
5. Admin notifications are generated immediately upon receiving an SOS alert
