-- Add disability fields to users table
ALTER TABLE `users` ADD `is_disabled` TINYINT(1) NOT NULL DEFAULT '0' AFTER `is_deleted`;
ALTER TABLE `users` ADD `disabilities` TEXT NULL DEFAULT NULL AFTER `is_disabled`;

-- Add shared ride fields to rides table
ALTER TABLE `rides` ADD `is_shared` TINYINT(1) NOT NULL DEFAULT '0' AFTER `status`;
ALTER TABLE `rides` ADD `shared_ride_id` INT UNSIGNED NOT NULL DEFAULT '0' AFTER `is_shared`;

-- Create shared_rides table
CREATE TABLE `shared_rides` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `primary_ride_id` int UNSIGNED NOT NULL,
  `secondary_ride_id` int UNSIGNED NOT NULL,
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '0=pending, 1=accepted, 2=completed, 9=canceled',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Create ride_share_requests table
CREATE TABLE `ride_share_requests` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `requester_id` int UNSIGNED NOT NULL COMMENT 'User who sent the request',
  `recipient_id` int UNSIGNED NOT NULL COMMENT 'User who received the request',
  `ride_id` int UNSIGNED NOT NULL COMMENT 'Ride ID of the requester',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '0=pending, 1=accepted, 2=rejected',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Create ride_share_chats table
CREATE TABLE `ride_share_chats` (
  `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT,
  `shared_ride_id` int UNSIGNED NOT NULL,
  `sender_id` int UNSIGNED NOT NULL,
  `message` text COLLATE utf8mb4_general_ci,
  `image` varchar(255) COLLATE utf8mb4_general_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Add notification templates for ride sharing
INSERT INTO `notification_templates` (`act`, `name`, `subject`, `push_title`, `email_body`, `sms_body`, `push_body`, `shortcodes`, `email_status`, `sms_status`, `push_status`, `created_at`, `updated_at`) VALUES
('RIDE_SHARE_REQUEST', 'Ride Share Request', 'New Ride Share Request', 'New Ride Share Request', '<p>Hello {{fullname}},</p><p>You have received a ride share request from {{username}}.</p><p>Pickup Location: {{ride_pickup}}</p><p>Destination: {{ride_destination}}</p><p>Please check your app to accept or reject this request.</p><p>Thank you,<br />{{site_name}}</p>', 'Hello {{fullname}}, You have received a ride share request from {{username}}. Please check your app to accept or reject this request.', '{{username}} wants to share a ride with you from {{ride_pickup}} to {{ride_destination}}', '{"username":"Requester Username","ride_pickup":"Pickup Location","ride_destination":"Destination Location","fullname":"Recipient Fullname"}', 1, 1, 1, NOW(), NOW()),
('RIDE_SHARE_ACCEPTED', 'Ride Share Request Accepted', 'Ride Share Request Accepted', 'Ride Share Request Accepted', '<p>Hello {{fullname}},</p><p>Your ride share request has been accepted by {{username}}.</p><p>Pickup Location: {{ride_pickup}}</p><p>Destination: {{ride_destination}}</p><p>You can now chat with your ride partner in the app.</p><p>Thank you,<br />{{site_name}}</p>', 'Hello {{fullname}}, Your ride share request has been accepted by {{username}}. You can now chat with your ride partner in the app.', '{{username}} has accepted your ride share request', '{"username":"Accepter Username","ride_pickup":"Pickup Location","ride_destination":"Destination Location","fullname":"Requester Fullname"}', 1, 1, 1, NOW(), NOW()),
('RIDE_SHARE_REJECTED', 'Ride Share Request Rejected', 'Ride Share Request Rejected', 'Ride Share Request Rejected', '<p>Hello {{fullname}},</p><p>Your ride share request has been rejected by {{username}}.</p><p>You can try finding another ride partner in the app.</p><p>Thank you,<br />{{site_name}}</p>', 'Hello {{fullname}}, Your ride share request has been rejected by {{username}}. You can try finding another ride partner in the app.', '{{username}} has rejected your ride share request', '{"username":"Rejecter Username","fullname":"Requester Fullname"}', 1, 1, 1, NOW(), NOW()),
('RIDE_SHARE_MESSAGE', 'Ride Share Message', 'New Message from Ride Share Partner', 'New Message from Ride Share Partner', '<p>Hello {{fullname}},</p><p>You have received a new message from your ride share partner {{username}}:</p><p>"{{message}}"</p><p>Please check your app to continue the conversation.</p><p>Thank you,<br />{{site_name}}</p>', 'Hello {{fullname}}, You have received a new message from your ride share partner {{username}}: "{{message}}"', '{{username}}: {{message}}', '{"username":"Sender Username","message":"Message Content","fullname":"Recipient Fullname"}', 1, 1, 1, NOW(), NOW());
