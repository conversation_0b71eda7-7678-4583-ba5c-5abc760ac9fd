# Ride Preference Feature Documentation

## Overview

The Ride Preference feature allows users to specify their preferences for a ride when creating a ride request. This information is displayed to drivers, helping them understand the user's needs and expectations before accepting the ride.

## Purpose

The preference field serves as an additional communication channel between riders and drivers, allowing riders to specify:
- Temperature preferences (e.g., "Please keep AC at medium")
- Music preferences (e.g., "I prefer quiet rides" or "I enjoy jazz music")
- Conversation preferences (e.g., "I prefer not to chat" or "Happy to chat")
- Special requests (e.g., "Please drive slowly, I get motion sickness")
- Any other preferences that might enhance the ride experience

## Implementation Details

### Database Changes

A new `preference` column has been added to the `rides` table:
- Column name: `preference`
- Type: `text`
- Nullable: `true`
- Added after the `note` column

### API Endpoints

#### 1. Create Ride

The create ride endpoint now accepts an optional `preference` parameter:

**Endpoint:** `POST /api/ride/create`

**Request Parameters:**
```json
{
    "service_id": 1,
    "pickup_latitude": 37.7749,
    "pickup_longitude": -122.4194,
    "destination_latitude": 37.3352,
    "destination_longitude": -121.8811,
    "note": "Please pick me up at the main entrance",
    "preference": "I prefer quiet rides with AC on medium",
    "number_of_passenger": 1,
    "offer_amount": 25.00,
    "payment_type": 1
}
```

### Response Format

The preference field is included in the ride details response for both users and drivers:

**User Ride Details Response:**
```json
{
    "status": "success",
    "message": "ride_details",
    "data": {
        "ride": {
            "id": 123,
            "uid": "RIDE123456",
            "pickup_location": "123 Main St",
            "destination": "456 Oak Ave",
            "note": "Please pick me up at the main entrance",
            "preference": "I prefer quiet rides with AC on medium",
            "amount": 25.00,
            "status": 1
        },
        "user_preference": "I prefer quiet rides with AC on medium",
        "waypoints": null,
        "total_stops": 0
    }
}
```

**Driver Ride Details Response:**
```json
{
    "status": "success",
    "message": "ride_details",
    "data": {
        "ride": {
            "id": 123,
            "uid": "RIDE123456",
            "pickup_location": "123 Main St",
            "destination": "456 Oak Ave",
            "note": "Please pick me up at the main entrance",
            "preference": "I prefer quiet rides with AC on medium",
            "amount": 25.00,
            "status": 1
        },
        "user_preference": "I prefer quiet rides with AC on medium",
        "user_disability": null
    }
}
```

### Admin Interface

The preference field is displayed in the admin ride details view, allowing administrators to see the user's preferences for a particular ride.

## Usage Guidelines

### For Riders

- Use the preference field to communicate specific preferences that would enhance your ride experience
- Keep preferences concise and clear
- Avoid including sensitive personal information
- Examples of good preferences:
  - "I prefer quiet rides"
  - "Please keep AC at medium temperature"
  - "I enjoy light conversation"
  - "Please drive carefully, I get motion sickness"

### For Drivers

- Review the user's preferences before starting the ride
- Try to accommodate reasonable preferences when possible
- Use the preference information to provide a better service experience
- If you cannot accommodate a preference, politely explain why to the rider

## Technical Implementation

The preference field is implemented as a simple text field that:
1. Is optional for users when creating a ride
2. Is passed through to the driver in the ride details
3. Is stored in the database for future reference
4. Is displayed in the admin interface

No additional validation is performed on the preference field beyond ensuring it is a string, allowing users flexibility in how they express their preferences.

## Future Enhancements

Potential future enhancements to the preference feature could include:
- Structured preference options (e.g., dropdown selections for common preferences)
- Preference templates that users can quickly select
- Preference profiles that users can save and reuse
- Driver preference matching to pair riders with drivers who can best accommodate their preferences
