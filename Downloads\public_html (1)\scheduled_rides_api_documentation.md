# Scheduled Rides Feature - API Documentation

## Overview

This document provides comprehensive details on the newly implemented scheduled rides feature, including API endpoints, request/response formats, and Pusher notification integration.

## Table of Contents
1. [Authentication](#authentication)
2. [Creating a Scheduled Ride](#creating-a-scheduled-ride)
3. [Listing Scheduled Rides](#listing-scheduled-rides)
4. [Viewing Ride Details](#viewing-ride-details)
5. [Canceling a Scheduled Ride](#canceling-a-scheduled-ride)
6. [Pusher Notification Integration](#pusher-notification-integration)
7. [Notification Events](#notification-events)

## Authentication

All API requests require authentication using a Bearer token.

### Login Request
```
POST /api/login
```

#### Headers
```
Content-Type: application/json
Accept: application/json
```

#### Body
```json
{
    "username": "your_username",
    "password": "your_password"
}
```

#### Response
```json
{
    "status": "success",
    "message": ["Login successful"],
    "data": {
        "user": {
            "id": 45,
            "firstname": "<PERSON>",
            "lastname": "<PERSON><PERSON>",
            "username": "johndo<PERSON>",
            "email": "<EMAIL>",
            "mobile": "+1234567890",
            "balance": "0.00000000",
            "image": null
        },
        "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
        "token_type": "Bearer"
    }
}
```

## Creating a Scheduled Ride

### Request
```
POST /api/ride/create
```

#### Headers
```
Content-Type: application/json
Accept: application/json
Authorization: Bearer {your_token}
```

#### Body
```json
{
    "service_id": 1,
    "pickup_latitude": 6.5244,
    "pickup_longitude": 3.3792,
    "destination_latitude": 6.4698,
    "destination_longitude": 3.5852,
    "note": "This is a scheduled ride",
    "number_of_passenger": 1,
    "offer_amount": 2000,
    "payment_type": 2,
    "is_scheduled": true,
    "scheduled_at": "2024-12-05T14:30:00"
}
```

#### Parameters Explanation
- `service_id`: ID of the service type (e.g., economy, premium)
- `pickup_latitude` & `pickup_longitude`: Coordinates of pickup location
- `destination_latitude` & `destination_longitude`: Coordinates of destination
- `note`: Optional note for the driver
- `number_of_passenger`: Number of passengers
- `offer_amount`: Amount offered for the ride
- `payment_type`: 1 = gateway, 2 = cash, 3 = wallet
- `is_scheduled`: Set to `true` to create a scheduled ride
- `scheduled_at`: Date and time when the ride should be scheduled (must be in the future)

#### Response
```json
{
    "status": "success",
    "message": ["Scheduled ride created successfully"],
    "data": {
        "ride": {
            "id": 123,
            "uid": "ABC123XYZ",
            "user_id": 45,
            "service_id": 1,
            "pickup_location": "Lagos Island, Lagos",
            "destination": "Lekki, Lagos",
            "is_scheduled": true,
            "scheduled_at": "2024-12-05T14:30:00.000000Z",
            "schedule_status": 0,
            "status": 0,
            "created_at": "2024-12-01T10:15:22.000000Z",
            "updated_at": "2024-12-01T10:15:22.000000Z"
        }
    }
}
```

## Listing Scheduled Rides

### Request
```
GET /api/ride/scheduled
```

#### Headers
```
Accept: application/json
Authorization: Bearer {your_token}
```

#### Response
```json
{
    "status": "success",
    "message": ["Get the scheduled ride list"],
    "data": {
        "rides": {
            "current_page": 1,
            "data": [
                {
                    "id": 123,
                    "uid": "ABC123XYZ",
                    "user_id": 45,
                    "service_id": 1,
                    "pickup_location": "Lagos Island, Lagos",
                    "destination": "Lekki, Lagos",
                    "is_scheduled": true,
                    "scheduled_at": "2024-12-05T14:30:00.000000Z",
                    "schedule_status": 0,
                    "status": 0,
                    "created_at": "2024-12-01T10:15:22.000000Z",
                    "updated_at": "2024-12-01T10:15:22.000000Z",
                    "driver": null,
                    "user": {...},
                    "service": {...}
                }
            ],
            "first_page_url": "...",
            "from": 1,
            "last_page": 1,
            "last_page_url": "...",
            "links": [...],
            "next_page_url": null,
            "path": "...",
            "per_page": 15,
            "prev_page_url": null,
            "to": 1,
            "total": 1
        }
    }
}
```

## Viewing Ride Details

### Request
```
GET /api/ride/details/{ride_id}
```

#### Headers
```
Accept: application/json
Authorization: Bearer {your_token}
```

#### Response
```json
{
    "status": "success",
    "message": ["Ride Details"],
    "data": {
        "ride": {
            "id": 123,
            "uid": "ABC123XYZ",
            "user_id": 45,
            "service_id": 1,
            "pickup_location": "Lagos Island, Lagos",
            "destination": "Lekki, Lagos",
            "is_scheduled": true,
            "scheduled_at": "2024-12-05T14:30:00.000000Z",
            "schedule_status": 0,
            "status": 0,
            "created_at": "2024-12-01T10:15:22.000000Z",
            "updated_at": "2024-12-01T10:15:22.000000Z",
            "bids": [],
            "user": {...},
            "service": {...}
        }
    }
}
```

## Canceling a Scheduled Ride

### Request
```
POST /api/ride/cancel/{ride_id}
```

#### Headers
```
Content-Type: application/json
Accept: application/json
Authorization: Bearer {your_token}
```

#### Body
```json
{
    "cancel_reason": "Changed my plans"
}
```

#### Response
```json
{
    "status": "success",
    "message": ["Ride canceled successfully"],
    "data": {}
}
```

## Pusher Notification Integration

The scheduled rides feature uses Pusher for real-time notifications. Notifications are sent at different intervals before the scheduled ride time (60min, 30min, 25min, 10min, 5min, 1min).

### Pusher Configuration

To receive real-time notifications, configure Pusher in your app using the credentials from the general settings:

```
GET /api/general-setting
```

Look for the `pusher_config` section in the response:

```json
{
    "pusher_config": {
        "app_id": "your_app_id",
        "app_key": "your_app_key",
        "app_secret": "your_app_secret",
        "cluster": "your_cluster"
    }
}
```

### Setting Up Pusher in Your App

#### For Android (Kotlin)

1. Add dependencies to your app-level `build.gradle`:

```gradle
dependencies {
    implementation 'com.pusher:pusher-java-client:2.4.0'
    implementation 'com.google.code.gson:gson:2.9.0'
}
```

2. Initialize Pusher in your application:

```kotlin
import com.pusher.client.Pusher
import com.pusher.client.PusherOptions
import com.pusher.client.channel.Channel
import com.pusher.client.channel.PusherEvent
import com.pusher.client.channel.SubscriptionEventListener
import com.pusher.client.connection.ConnectionEventListener
import com.pusher.client.connection.ConnectionState
import com.pusher.client.connection.ConnectionStateChange

// Initialize Pusher
val options = PusherOptions()
    .setCluster(pusherCluster)
val pusher = Pusher(pusherAppKey, options)

// Connect to Pusher
pusher.connect(object : ConnectionEventListener {
    override fun onConnectionStateChange(change: ConnectionStateChange) {
        Log.d("Pusher", "State changed from ${change.previousState} to ${change.currentState}")
    }

    override fun onError(message: String, code: String, e: Exception) {
        Log.e("Pusher", "Error: $message, code: $code", e)
    }
}, ConnectionState.ALL)
```

#### For iOS (Swift)

1. Add PusherSwift to your project using Swift Package Manager, CocoaPods, or Carthage.

Using CocoaPods:
```ruby
pod 'PusherSwift', '~> 10.1.0'
```

2. Initialize Pusher in your application:

```swift
import PusherSwift

// Initialize Pusher
let options = PusherClientOptions(
    host: .cluster(pusherCluster)
)
let pusher = Pusher(key: pusherAppKey, options: options)

// Connect to Pusher
pusher.connect()
```

### Subscribing to Channels

#### For User Notifications

##### Android (Kotlin)
```kotlin
// Subscribe to user channel
val userChannel = pusher.subscribe("user-${userId}")

// Listen for events
userChannel.bind("new-ride", object : SubscriptionEventListener {
    override fun onEvent(event: PusherEvent) {
        val data = event.data
        // Parse the data and handle the notification
        Log.d("Pusher", "Received event: $data")
    }
})
```

##### iOS (Swift)
```swift
// Subscribe to user channel
let userChannel = pusher.subscribe("user-\(userId)")

// Listen for events
userChannel.bind(eventName: "new-ride") { data in
    if let dataString = data as? String,
       let jsonData = dataString.data(using: .utf8) {
        // Parse the data and handle the notification
        print("Received event: \(dataString)")
    }
}
```

#### For Driver Notifications (if a driver accepts the scheduled ride)

##### Android (Kotlin)
```kotlin
// Subscribe to driver channel
val driverChannel = pusher.subscribe("new-ride-for-driver-${driverId}")

// Listen for events
driverChannel.bind("new-ride", object : SubscriptionEventListener {
    override fun onEvent(event: PusherEvent) {
        val data = event.data
        // Parse the data and handle the notification
        Log.d("Pusher", "Received event: $data")
    }
})
```

##### iOS (Swift)
```swift
// Subscribe to driver channel
let driverChannel = pusher.subscribe("new-ride-for-driver-\(driverId)")

// Listen for events
driverChannel.bind(eventName: "new-ride") { data in
    if let dataString = data as? String,
       let jsonData = dataString.data(using: .utf8) {
        // Parse the data and handle the notification
        print("Received event: \(dataString)")
    }
}
```

### Handling Pusher Authentication (if needed)

If your channels require authentication:

#### Android (Kotlin)
```kotlin
val options = PusherOptions()
    .setCluster(pusherCluster)
    .setAuthorizer(object : Authorizer {
        override fun authorize(channelName: String, socketId: String): String {
            // Make an API call to your auth endpoint
            val response = yourApiClient.authorizeChannel(channelName, socketId)
            return response.body
        }
    })
```

#### iOS (Swift)
```swift
let options = PusherClientOptions(
    host: .cluster(pusherCluster),
    authMethod: AuthMethod.endpoint(authEndpoint: "/api/pusher/auth")
)
```

### Disconnecting from Pusher

Don't forget to disconnect when appropriate:

#### Android (Kotlin)
```kotlin
override fun onDestroy() {
    super.onDestroy()
    pusher.disconnect()
}
```

#### iOS (Swift)
```swift
deinit {
    pusher.disconnect()
}
```

## Notification Events

### 1. Scheduled Ride Creation

When a scheduled ride is created, the user receives a notification:

#### Event: `new-ride`
#### Channel: `user-{user_id}`
#### Data:
```json
{
    "ride": {
        "id": 123,
        "uid": "ABC123XYZ",
        "user_id": 45,
        "service_id": 1,
        "pickup_location": "Lagos Island, Lagos",
        "destination": "Lekki, Lagos",
        "is_scheduled": true,
        "scheduled_at": "2024-12-05T14:30:00.000000Z",
        "schedule_status": 0,
        "status": 0
    },
    "notification_type": "scheduled_ride_created",
    "user_image_path": "/assets/images/user/"
}
```

### 2. Scheduled Ride Reminders

As the scheduled time approaches, both the user and driver (if assigned) receive reminder notifications:

#### Event: `new-ride`
#### Channel: `user-{user_id}` or `new-ride-for-driver-{driver_id}`
#### Data:
```json
{
    "ride": {
        "id": 123,
        "uid": "ABC123XYZ",
        "user_id": 45,
        "service_id": 1,
        "pickup_location": "Lagos Island, Lagos",
        "destination": "Lekki, Lagos",
        "is_scheduled": true,
        "scheduled_at": "2024-12-05T14:30:00.000000Z",
        "schedule_status": 1,
        "status": 0
    },
    "notification_type": "scheduled_ride_reminder",
    "time_remaining": "60 minutes", // Can be "60 minutes", "30 minutes", "25 minutes", "10 minutes", "5 minutes", or "1 minute"
    "user_image_path": "/assets/images/user/",
    "driver_image_path": "/assets/images/driver/" // Only included in driver notifications
}
```

## Important Notes

1. The `scheduled_at` parameter must be a future date and time.
2. Scheduled rides will not immediately notify drivers. Drivers will be notified when the scheduled time approaches.
3. Users can view their scheduled rides through the `/api/ride/scheduled` endpoint.
4. The notification system uses both standard notifications (email, SMS) and Pusher for real-time updates.
5. The schedule status is updated as notifications are sent at different time intervals:
   - 0: Pending (no notifications sent yet)
   - 1: 60-minute notification sent
   - 2: 30-minute notification sent
   - 3: 25-minute notification sent
   - 4: 10-minute notification sent
   - 5: 5-minute notification sent
   - 6: 1-minute notification sent
   - 7: Completed

This documentation provides all the necessary information to integrate the scheduled rides feature into your mobile application, including API endpoints and Pusher notification handling.
