<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;

class Message extends Model
{
    protected $guarded = ['id'];

    public function ride()
    {
        return $this->belongsTo(Ride::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function driver()
    {
        return $this->belongsTo(Driver::class);
    }

    public function hasVideo(): Attribute
    {
        return new Attribute(
            get: fn() => !empty($this->video),
        );
    }

    public function hasImage(): Attribute
    {
        return new Attribute(
            get: fn() => !empty($this->image),
        );
    }

    public function videoUrl(): Attribute
    {
        return new Attribute(
            get: fn() => $this->video ? getFilePath('message') . '/' . $this->video : null,
        );
    }

    public function videoThumbnailUrl(): Attribute
    {
        return new Attribute(
            get: fn() => $this->video_thumbnail ? getFilePath('message') . '/' . $this->video_thumbnail : null,
        );
    }

    public function imageUrl(): Attribute
    {
        return new Attribute(
            get: fn() => $this->image ? getFilePath('message') . '/' . $this->image : null,
        );
    }
}
