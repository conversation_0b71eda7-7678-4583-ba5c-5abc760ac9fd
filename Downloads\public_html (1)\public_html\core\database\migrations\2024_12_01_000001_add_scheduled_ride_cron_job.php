<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\CronJob;
use Carbon\Carbon;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add the scheduled ride notifications cron job
        $cronJob = new CronJob();
        $cronJob->name = 'Scheduled Ride Notifications';
        $cronJob->alias = 'scheduled_ride_notifications';
        $cronJob->action = ['App\\Http\\Controllers\\CronController', 'scheduledRideNotifications'];
        $cronJob->next_run = Carbon::now()->addMinutes(1)->toDateTimeString();
        $cronJob->cron_schedule_id = 1; // Using the default schedule (adjust if needed)
        $cronJob->is_running = 1;
        $cronJob->is_default = 1;
        $cronJob->save();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove the scheduled ride notifications cron job
        CronJob::where('alias', 'scheduled_ride_notifications')->delete();
    }
};
