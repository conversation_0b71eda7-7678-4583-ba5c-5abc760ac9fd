<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('rides', function (Blueprint $table) {
            // Modify max_amount column to allow null values (remove maximum limits)
            $table->decimal('max_amount', 28, 8)->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('rides', function (Blueprint $table) {
            // Revert back to NOT NULL with default value
            $table->decimal('max_amount', 28, 8)->default(0.00000000)->change();
        });
    }
};
