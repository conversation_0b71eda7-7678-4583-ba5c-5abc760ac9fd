<?php

namespace App\Http\Controllers\Api\Driver;

use App\Models\Ride;
use App\Constants\Status;
use App\Events\Ride as EventsRide;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Lib\RidePaymentManager;
use App\Models\SharedRide;
use App\Models\Zone;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class RideController extends Controller
{
    public function details($id)
    {
        $ride = Ride::with(['bids', 'user', 'driver', 'service', 'userReview', 'driverReview', 'driver.brand', 'service', 'sharedRide.primaryRide.user', 'sharedRide.secondaryRide.user'])->find($id);

        if (!$ride) {
            $notify[] = 'This ride is unavailable';
            return apiResponse("not_found", 'error', $notify);
        }

        $notify[] = 'Ride Details';

        // Include disability information in the response
        $userDisabilityInfo = null;
        if ($ride->user && isset($ride->user->is_disabled) && $ride->user->is_disabled) {
            $userDisabilityInfo = [
                'is_disabled' => true,
                'disabilities' => $ride->user->disabilities
            ];
        }

        // Include user preference if available
        $userPreference = null;
        if ($ride->preference) {
            $userPreference = $ride->preference;
        }

        // Create a combined view for shared rides
        $combinedRideInfo = null;
        $sharedRideInfo = null;

        if ($ride->is_shared && $ride->sharedRide) {
            $partnerRide = null;
            $partnerUser = null;

            if ($ride->id == $ride->sharedRide->primary_ride_id) {
                $partnerRide = $ride->sharedRide->secondaryRide;
                $partnerUser = $ride->sharedRide->secondaryRide->user;
            } else {
                $partnerRide = $ride->sharedRide->primaryRide;
                $partnerUser = $ride->sharedRide->primaryRide->user;
            }

            // Check for partner's disability information
            $partnerDisabilityInfo = null;
            if (isset($partnerUser->is_disabled) && $partnerUser->is_disabled) {
                $partnerDisabilityInfo = [
                    'is_disabled' => true,
                    'disabilities' => $partnerUser->disabilities
                ];
            }

            // Create shared ride info (for backward compatibility)
            $sharedRideInfo = [
                'id' => $ride->sharedRide->id,
                'status' => $ride->sharedRide->status,
                'partner' => [
                    'id' => $partnerUser->id,
                    'name' => $partnerUser->fullname,
                    'username' => $partnerUser->username,
                    'image' => $partnerUser->image,
                    'disability' => $partnerDisabilityInfo,
                ],
                'partner_ride' => [
                    'id' => $partnerRide->id,
                    'pickup_location' => $partnerRide->pickup_location,
                    'pickup_latitude' => $partnerRide->pickup_latitude,
                    'pickup_longitude' => $partnerRide->pickup_longitude,
                    'destination' => $partnerRide->destination,
                    'destination_latitude' => $partnerRide->destination_latitude,
                    'destination_longitude' => $partnerRide->destination_longitude,
                ],
            ];

            // Create a combined view for the driver
            $combinedRideInfo = [
                'id' => $ride->sharedRide->id,
                'status' => $ride->sharedRide->status,
                'is_shared' => true,
                'total_fare' => $ride->amount + $partnerRide->amount,
                'commission_percentage' => $ride->commission_percentage,
                'service' => [
                    'id' => $ride->service_id,
                    'name' => $ride->service->name ?? 'Unknown'
                ],
                'passengers' => [
                    [
                        'id' => $ride->user->id,
                        'name' => $ride->user->fullname,
                        'username' => $ride->user->username,
                        'image' => $ride->user->image,
                        'disability' => $userDisabilityInfo,
                        'ride_id' => $ride->id,
                        'pickup' => [
                            'location' => $ride->pickup_location,
                            'latitude' => $ride->pickup_latitude,
                            'longitude' => $ride->pickup_longitude,
                        ],
                        'destination' => [
                            'location' => $ride->destination,
                            'latitude' => $ride->destination_latitude,
                            'longitude' => $ride->destination_longitude,
                        ],
                        'amount' => $ride->amount,
                        'payment_status' => $ride->payment_status,
                        'payment_type' => $ride->payment_type,
                    ],
                    [
                        'id' => $partnerUser->id,
                        'name' => $partnerUser->fullname,
                        'username' => $partnerUser->username,
                        'image' => $partnerUser->image,
                        'disability' => $partnerDisabilityInfo,
                        'ride_id' => $partnerRide->id,
                        'pickup' => [
                            'location' => $partnerRide->pickup_location,
                            'latitude' => $partnerRide->pickup_latitude,
                            'longitude' => $partnerRide->pickup_longitude,
                        ],
                        'destination' => [
                            'location' => $partnerRide->destination,
                            'latitude' => $partnerRide->destination_latitude,
                            'longitude' => $partnerRide->destination_longitude,
                        ],
                        'amount' => $partnerRide->amount,
                        'payment_status' => $partnerRide->payment_status,
                        'payment_type' => $partnerRide->payment_type,
                    ],
                ],
                'common_destination' => [
                    'location' => $ride->destination,
                    'latitude' => $ride->destination_latitude,
                    'longitude' => $ride->destination_longitude,
                ],
            ];
        }

        return apiResponse("ride_details", 'success', $notify, [
            'ride'            => $ride,
            'user_image_path' => getFilePath('user'),
            'user_disability' => $userDisabilityInfo,
            'user_preference' => $userPreference,
            'shared_ride'     => $sharedRideInfo,
            'combined_ride'   => $combinedRideInfo,
        ]);
    }

    public function start(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'otp' => 'required|digits:6'
        ]);

        if ($validator->fails()) {
            return apiResponse('validation_error', 'error', $validator->errors()->all());
        }

        $driver = Auth::user();
        $ride   = Ride::where('status', Status::RIDE_ACTIVE)->where('driver_id', $driver->id)->find($id);

        if (!$ride) {
            $notify[] = 'The ride not found or the ride not eligible to start yet';
            return apiResponse('not_found', 'error', $notify);
        }

        // Check if we're using subscription model and if driver has active subscription
        $general = gs();
        if ($general->revenue_model == Status::REVENUE_SUBSCRIPTION && !$driver->hasActiveSubscription()) {
            $notify[] = "You need an active subscription to start rides. Please subscribe to continue.";
            return apiResponse("subscription_required", "error", $notify, [
                'subscription_fee' => showAmount($general->driver_subscription_fee)
            ]);
        }

        $hasRunningRide = Ride::running()->where('driver_id', $driver->id)->first();

        if ($hasRunningRide) {
            $notify[] = 'You have another running ride. You have to complete that running ride first.';
            return apiResponse('complete', 'error', $notify);
        }

        if ($ride->otp != $request->otp) {
            $notify[] = 'The OTP code is invalid';
            return apiResponse('invalid', 'error', $notify);
        }

        $commission              = $ride->amount / 100 * $ride->commission_percentage;
        $ride->start_time        = now();
        $ride->status            = Status::RIDE_RUNNING;
        $ride->commission_amount = $commission;
        $ride->save();

        initializePusher();

        $ride->load('driver', 'driver.brand', 'service', 'user');

        event(new EventsRide($ride, 'pick_up'));

        notify($ride->user, 'START_RIDE', [
            'ride_id'         => $ride->uid,
            'amount'          => showAmount($ride->amount, currencyFormat: false),
            'rider'           => $ride->user->username,
            'service'         => $ride->service->name,
            'pickup_location' => $ride->pickup_location,
            'destination'     => $ride->destination,
            'duration'        => $ride->duration,
            'distance'        => $ride->distance,
            'pickup_time'     => showDateTime(now())
        ]);

        $notify[] = 'The ride has been started';
        return apiResponse("ride_start", "success", $notify);
    }

    public function end(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'latitude'  => 'required|numeric',
            'longitude' => 'required|numeric',
        ]);

        if ($validator->fails()) {
            return apiResponse("validation_error", 'error', $validator->errors()->all());
        }


        $driver = Auth::user();
        $ride   = Ride::running()
            ->where('driver_id', $driver->id)
            ->find($id);

        if (!$ride) {
            $notify[] = 'The ride not found';
            return apiResponse('not_found', 'error', $notify);
        }

        // update the driver current zone
        if ($ride->ride_type == Status::INTER_CITY_RIDE) {
            $zones       = Zone::active()->get();
            $address     = ['lat' => $request->latitude, 'long' => $request->longitude];
            $currentZone = null;

            foreach ($zones as $zone) {
                $findZone = insideZone($address, $zone);
                if ($findZone) {
                    $currentZone = $zone;
                    break;
                }
            }
            if ($currentZone) {
                $driver->zone_id = $currentZone->id;
                $driver->save();
            }
        }

        $ride->payment_status = Status::PAYMENT_PENDING;
        $ride->status         = Status::RIDE_END;
        $ride->end_time       = now();
        $ride->save();

        initializePusher();

        $ride->load('driver', 'driver.brand', 'service', 'user');

        event(new EventsRide($ride, 'ride_end'));


        $notify[] = 'The ride is now available for payment';
        return apiResponse("ride_complete", 'success', $notify);
    }

    public function list()
    {
        $driver = Auth::user();
        $query  = Ride::with('user')->orderBy('id', 'desc')->where('service_id', @$driver->service_id);

        if (request()->status == 'accept') {
            $query->pending()
                ->whereHas('bids', function ($q) use ($driver) {
                    $q->where('status', Status::BID_PENDING)->where("driver_id", $driver->id);
                })
                ->filter(['ride_type']);
        } elseif (request()->status == 'new') {
            $query->where('pickup_zone_id', $driver->zone_id)
                ->pending()
                ->whereDoesntHave('bids', function ($q) use ($driver) {
                    $q->where("driver_id", $driver->id);
                })
                ->filter(['ride_type']);
        } elseif (request()->status == 'all') {
            $query->where('driver_id', Auth::id())
                ->orWhereHas('bids', function ($q) use ($driver) {
                    $q->where('status', Status::BID_PENDING)->where("driver_id", $driver->id);
                })
                ->filter(['ride_type']);
        } else {
            $query->where('driver_id', Auth::id())->filter(['status', 'ride_type']);
        }

        $rides    = $query->paginate(getPaginate());
        $notify[] = 'Ride list';

        if (request()->status == 'new' && $driver->online_status != Status::YES) {
            $rides = null;
        }

        // Process rides to include disability information and shared ride information
        $processedRides = $rides ? $rides->toArray() : null;

        if ($processedRides && isset($processedRides['data']) && count($processedRides['data']) > 0) {
            foreach ($processedRides['data'] as $key => $ride) {
                // Add disability information
                if (isset($ride['user']) && isset($ride['user']['is_disabled']) && $ride['user']['is_disabled']) {
                    $processedRides['data'][$key]['user_disability'] = [
                        'is_disabled' => true,
                        'disabilities' => $ride['user']['disabilities']
                    ];
                } else {
                    $processedRides['data'][$key]['user_disability'] = null;
                }

                // Add combined ride information for shared rides
                if (isset($ride['is_shared']) && $ride['is_shared'] && isset($ride['shared_ride_id']) && $ride['shared_ride_id'] > 0) {
                    // Load the actual ride object to get shared ride details
                    $rideObj = Ride::with(['sharedRide.primaryRide.user', 'sharedRide.secondaryRide.user'])->find($ride['id']);

                    if ($rideObj && $rideObj->sharedRide) {
                        $partnerRide = null;
                        $partnerUser = null;

                        if ($rideObj->id == $rideObj->sharedRide->primary_ride_id) {
                            $partnerRide = $rideObj->sharedRide->secondaryRide;
                            $partnerUser = $rideObj->sharedRide->secondaryRide->user;
                        } else {
                            $partnerRide = $rideObj->sharedRide->primaryRide;
                            $partnerUser = $rideObj->sharedRide->primaryRide->user;
                        }

                        // Check for partner's disability information
                        $partnerDisabilityInfo = null;
                        if (isset($partnerUser->is_disabled) && $partnerUser->is_disabled) {
                            $partnerDisabilityInfo = [
                                'is_disabled' => true,
                                'disabilities' => $partnerUser->disabilities
                            ];
                        }

                        // Add combined ride information
                        $processedRides['data'][$key]['combined_ride'] = [
                            'id' => $rideObj->sharedRide->id,
                            'status' => $rideObj->sharedRide->status,
                            'is_shared' => true,
                            'total_fare' => $rideObj->amount + $partnerRide->amount,
                            'passengers' => [
                                [
                                    'id' => $rideObj->user->id,
                                    'name' => $rideObj->user->fullname,
                                    'username' => $rideObj->user->username,
                                    'image' => $rideObj->user->image,
                                    'ride_id' => $rideObj->id,
                                    'pickup' => [
                                        'location' => $rideObj->pickup_location,
                                        'latitude' => $rideObj->pickup_latitude,
                                        'longitude' => $rideObj->pickup_longitude,
                                    ],
                                ],
                                [
                                    'id' => $partnerUser->id,
                                    'name' => $partnerUser->fullname,
                                    'username' => $partnerUser->username,
                                    'image' => $partnerUser->image,
                                    'disability' => $partnerDisabilityInfo,
                                    'ride_id' => $partnerRide->id,
                                    'pickup' => [
                                        'location' => $partnerRide->pickup_location,
                                        'latitude' => $partnerRide->pickup_latitude,
                                        'longitude' => $partnerRide->pickup_longitude,
                                    ],
                                ],
                            ],
                            'common_destination' => [
                                'location' => $rideObj->destination,
                                'latitude' => $rideObj->destination_latitude,
                                'longitude' => $rideObj->destination_longitude,
                            ],
                        ];
                    }
                }
            }
        }

        return apiResponse('ride_list', 'success', $notify, [
            'rides'           => $processedRides,
            'user_image_path' => getFilePath('user'),
        ]);
    }

    public function receivedCashPayment($id)
    {
        $driver = Auth::user();
        $ride   = Ride::where('status', Status::RIDE_END)->where('driver_id', $driver->id)->find($id);

        if (!$ride) {
            $notify[] = 'The ride not found';
            return apiResponse('not_found', 'error', $notify);
        }

        if (!$ride) {
            $notify[] = 'The ride is invalid';
            return apiResponse('not_found', 'error', $notify);
        }

        // Check if this is a shared ride
        if ($ride->is_shared && $ride->sharedRide) {
            // For shared rides, we need to handle both rides
            $partnerRide = null;

            if ($ride->id == $ride->sharedRide->primary_ride_id) {
                $partnerRide = Ride::find($ride->sharedRide->secondary_ride_id);
            } else {
                $partnerRide = Ride::find($ride->sharedRide->primary_ride_id);
            }

            // Process payment for both rides
            if ($partnerRide) {
                // Calculate half fare for each ride
                $halfAmount = $ride->amount / 2;

                // Process payment for both rides
                (new RidePaymentManager())->payment($ride, Status::PAYMENT_TYPE_CASH, $halfAmount);
                (new RidePaymentManager())->payment($partnerRide, Status::PAYMENT_TYPE_CASH, $halfAmount);

                // Update shared ride status
                $ride->sharedRide->status = SharedRide::STATUS_COMPLETED;
                $ride->sharedRide->save();

                // Send notifications for both rides
                initializePusher();

                $ride->load('bids', 'user', 'driver', 'service', 'userReview', 'driverReview', 'driver.brand', 'sharedRide');
                $partnerRide->load('user');

                // Emit events for both rides
                event(new EventsRide($ride, 'shared-ride-cash-payment-received'));
                event(new EventsRide($partnerRide, 'shared-ride-cash-payment-received'));
            } else {
                // If partner ride not found, process as normal ride
                (new RidePaymentManager())->payment($ride, Status::PAYMENT_TYPE_CASH);

                initializePusher();

                $ride->load('bids', 'user', 'driver', 'service', 'userReview', 'driverReview', 'driver.brand');
                event(new EventsRide($ride, 'cash-payment-received'));
            }
        } else {
            // Regular ride payment
            (new RidePaymentManager())->payment($ride, Status::PAYMENT_TYPE_CASH);

            initializePusher();

            $ride->load('bids', 'user', 'driver', 'service', 'userReview', 'driverReview', 'driver.brand');
            event(new EventsRide($ride, 'cash-payment-received'));
        }

        $notify[] = 'Payment received successfully';
        return apiResponse('payment_received', 'success', $notify, [
            'ride' => $ride
        ]);
    }

    public function liveLocation(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'latitude'  => 'required|numeric',
            'longitude' => 'required|numeric',
        ]);

        if ($validator->fails()) {
            return apiResponse("validation_error", 'error', $validator->errors()->all());
        }

        $ride   = Ride::find($id);
        if (!$ride) {
            $notify[] = 'The ride not found';
            return apiResponse('not_found', 'error', $notify);
        }

        if ($ride->status == Status::RIDE_ACTIVE || $ride->status == Status::RIDE_RUNNING) {
            initializePusher();
            event(new EventsRide($ride, 'live_location', $request->only(['latitude', 'longitude'])));
        }

        $notify[] = "live location change";
        return apiResponse("live_location", 'success', $notify);
    }
}
