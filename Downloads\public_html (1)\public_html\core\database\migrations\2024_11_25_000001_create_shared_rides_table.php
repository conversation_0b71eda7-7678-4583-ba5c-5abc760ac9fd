<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('shared_rides', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('primary_ride_id');
            $table->unsignedInteger('secondary_ride_id');
            $table->tinyInteger('status')->default(0)->comment('0=pending, 1=accepted, 2=completed, 9=canceled');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('shared_rides');
    }
};
