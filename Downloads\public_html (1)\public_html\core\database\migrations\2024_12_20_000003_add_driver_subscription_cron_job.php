<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\CronJob;
use App\Models\CronSchedule;
use Carbon\Carbon;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add the driver subscription cron job
        $cronJob = new CronJob();
        $cronJob->name = 'Driver Subscription Processor';
        $cronJob->alias = 'driver_subscription_processor';
        $cronJob->action = ['App\\Http\\Controllers\\CronController', 'processDriverSubscriptions'];
        $cronJob->next_run = Carbon::now()->addDay()->startOfDay()->toDateTimeString();
        $cronJob->cron_schedule_id = 1; // Using the default schedule (adjust if needed)
        $cronJob->is_running = 1;
        $cronJob->is_default = 1;
        $cronJob->save();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove the driver subscription cron job
        CronJob::where('alias', 'driver_subscription_processor')->delete();
    }
};
