<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('services', function (Blueprint $table) {
            // Modify max_fare columns to allow null values (remove maximum limits)
            $table->decimal('city_max_fare', 28, 8)->nullable()->change();
            $table->decimal('intercity_max_fare', 28, 8)->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('services', function (Blueprint $table) {
            // Revert back to NOT NULL with default values
            $table->decimal('city_max_fare', 28, 8)->default(0.00000000)->change();
            $table->decimal('intercity_max_fare', 28, 8)->default(0.00000000)->change();
        });
    }
};
