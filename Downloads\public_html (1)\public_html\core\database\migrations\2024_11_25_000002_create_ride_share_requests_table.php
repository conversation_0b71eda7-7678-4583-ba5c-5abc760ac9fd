<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ride_share_requests', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('requester_id')->comment('User who sent the request');
            $table->unsignedInteger('recipient_id')->comment('User who received the request');
            $table->unsignedInteger('ride_id')->comment('Ride ID of the requester');
            $table->tinyInteger('status')->default(0)->comment('0=pending, 1=accepted, 2=rejected');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ride_share_requests');
    }
};
