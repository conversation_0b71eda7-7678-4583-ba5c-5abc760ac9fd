<?php

namespace App\Http\Controllers\Api\User;

use App\Constants\Status;
use App\Events\Ride as EventsRide;
use App\Http\Controllers\Controller;
use App\Models\Ride;
use App\Models\RideTrackingCode;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class RideTrackingController extends Controller
{
    /**
     * Generate a tracking code for a ride
     */
    public function generateTrackingCode(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'ride_id' => 'required|exists:rides,id',
            'expires_in' => 'nullable|integer|min:1|max:1440', // Minutes (max 24 hours)
        ]);

        if ($validator->fails()) {
            return apiResponse("validation_error", 'error', $validator->errors()->all());
        }

        $user = Auth::user();
        $ride = Ride::find($request->ride_id);

        // Check if the ride belongs to the user
        if ($ride->user_id != $user->id) {
            $notify[] = 'You are not authorized to generate a tracking code for this ride';
            return apiResponse('unauthorized', 'error', $notify);
        }

        // Check if the ride is active or running
        if (!in_array($ride->status, [Status::RIDE_ACTIVE, Status::RIDE_RUNNING])) {
            $notify[] = 'Tracking codes can only be generated for active or running rides';
            return apiResponse('invalid_status', 'error', $notify);
        }

        // Generate a unique tracking code
        $trackingCode = getTrx(8);
        
        // Set expiration time (default 60 minutes if not specified)
        $expiresIn = $request->expires_in ?? 60;
        $expiresAt = Carbon::now()->addMinutes($expiresIn);

        // Create the tracking code record
        $rideTracking = new RideTrackingCode();
        $rideTracking->ride_id = $ride->id;
        $rideTracking->user_id = $user->id;
        $rideTracking->tracking_code = $trackingCode;
        $rideTracking->expires_at = $expiresAt;
        $rideTracking->save();

        // Enable tracking on the ride if not already enabled
        if (!$ride->tracking_enabled) {
            $ride->tracking_enabled = true;
            $ride->tracking_code = $trackingCode;
            $ride->save();
        }

        $notify[] = 'Tracking code generated successfully';
        return apiResponse('tracking_code_generated', 'success', $notify, [
            'tracking_code' => $trackingCode,
            'expires_at' => $expiresAt,
            'ride' => $ride
        ]);
    }

    /**
     * Track a ride using a tracking code
     */
    public function trackRide(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'tracking_code' => 'required|string|min:8|max:40',
        ]);

        if ($validator->fails()) {
            return apiResponse("validation_error", 'error', $validator->errors()->all());
        }

        // Find the tracking code
        $trackingCode = RideTrackingCode::where('tracking_code', $request->tracking_code)
            ->active()
            ->first();

        if (!$trackingCode) {
            $notify[] = 'Invalid or expired tracking code';
            return apiResponse('invalid_code', 'error', $notify);
        }

        // Get the ride
        $ride = $trackingCode->ride;

        // Check if the ride is still active or running
        if (!in_array($ride->status, [Status::RIDE_ACTIVE, Status::RIDE_RUNNING])) {
            $notify[] = 'This ride is no longer active';
            return apiResponse('inactive_ride', 'error', $notify);
        }

        // Load necessary relationships
        $ride->load('driver', 'service');

        $notify[] = 'Ride tracking information retrieved successfully';
        return apiResponse('tracking_info', 'success', $notify, [
            'ride' => [
                'id' => $ride->id,
                'uid' => $ride->uid,
                'status' => $ride->status,
                'pickup_location' => $ride->pickup_location,
                'destination' => $ride->destination,
                'driver' => [
                    'name' => $ride->driver->fullname,
                    'phone' => $ride->driver->mobileNumber,
                    'image' => $ride->driver->imageSrc,
                    'service' => $ride->service->name,
                ],
                'tracking_expires_at' => $trackingCode->expires_at,
            ]
        ]);
    }

    /**
     * Get the current location of a ride
     */
    public function getRideLocation(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'tracking_code' => 'required|string|min:8|max:40',
        ]);

        if ($validator->fails()) {
            return apiResponse("validation_error", 'error', $validator->errors()->all());
        }

        // Find the tracking code
        $trackingCode = RideTrackingCode::where('tracking_code', $request->tracking_code)
            ->active()
            ->first();

        if (!$trackingCode) {
            $notify[] = 'Invalid or expired tracking code';
            return apiResponse('invalid_code', 'error', $notify);
        }

        // Get the ride
        $ride = $trackingCode->ride;

        // Check if the ride is still active or running
        if (!in_array($ride->status, [Status::RIDE_ACTIVE, Status::RIDE_RUNNING])) {
            $notify[] = 'This ride is no longer active';
            return apiResponse('inactive_ride', 'error', $notify);
        }

        // Get the driver's current location
        $driver = $ride->driver;

        $notify[] = 'Driver location retrieved successfully';
        return apiResponse('driver_location', 'success', $notify, [
            'latitude' => $driver->latitude,
            'longitude' => $driver->longitude,
            'ride_status' => $ride->status,
        ]);
    }
}
