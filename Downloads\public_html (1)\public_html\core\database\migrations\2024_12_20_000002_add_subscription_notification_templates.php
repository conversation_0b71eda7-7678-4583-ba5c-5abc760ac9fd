<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add notification templates for driver subscriptions
        DB::table('notification_templates')->insert([
            [
                'act' => 'DRIVER_SUBSCRIPTION_PAYMENT',
                'name' => 'Driver Subscription Payment',
                'subject' => 'Your Weekly Subscription Payment',
                'push_title' => 'Subscription Payment Processed',
                'email_body' => '<p>Hello {{fullname}},</p><p>Your weekly subscription payment of {{amount}} has been processed successfully.</p><p>Your subscription is now active until {{end_date}}.</p><p>Thank you for using our service.</p><p>{{site_name}}</p>',
                'sms_body' => 'Hello {{fullname}}, Your weekly subscription payment of {{amount}} has been processed. Valid until {{end_date}}.',
                'push_body' => 'Your weekly subscription payment of {{amount}} has been processed successfully. Valid until {{end_date}}.',
                'shortcodes' => '{"fullname":"Driver Full Name","amount":"Subscription Amount","end_date":"Subscription End Date"}',
                'email_status' => 1,
                'sms_status' => 1,
                'push_status' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'act' => 'DRIVER_SUBSCRIPTION_EXPIRING',
                'name' => 'Driver Subscription Expiring',
                'subject' => 'Your Subscription is Expiring Soon',
                'push_title' => 'Subscription Expiring Soon',
                'email_body' => '<p>Hello {{fullname}},</p><p>Your subscription is expiring on {{end_date}}.</p><p>Please ensure you have sufficient balance ({{amount}}) in your account for automatic renewal.</p><p>If your balance is insufficient, you will not be able to accept new rides after expiration.</p><p>{{site_name}}</p>',
                'sms_body' => 'Hello {{fullname}}, Your subscription expires on {{end_date}}. Please ensure you have {{amount}} in your account for renewal.',
                'push_body' => 'Your subscription expires on {{end_date}}. Please ensure you have {{amount}} in your account for renewal.',
                'shortcodes' => '{"fullname":"Driver Full Name","amount":"Subscription Amount","end_date":"Subscription End Date"}',
                'email_status' => 1,
                'sms_status' => 1,
                'push_status' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'act' => 'DRIVER_SUBSCRIPTION_FAILED',
                'name' => 'Driver Subscription Payment Failed',
                'subject' => 'Subscription Payment Failed - Action Required',
                'push_title' => 'Subscription Payment Failed',
                'email_body' => '<p>Hello {{fullname}},</p><p>We could not process your subscription payment due to insufficient balance.</p><p>Required amount: {{amount}}</p><p>Your current balance: {{balance}}</p><p>Please add funds to your account to continue accepting rides.</p><p>{{site_name}}</p>',
                'sms_body' => 'Hello {{fullname}}, Your subscription payment of {{amount}} failed due to insufficient balance. Please add funds to continue accepting rides.',
                'push_body' => 'Your subscription payment failed due to insufficient balance. Please add {{amount}} to your account to continue accepting rides.',
                'shortcodes' => '{"fullname":"Driver Full Name","amount":"Subscription Amount","balance":"Current Balance"}',
                'email_status' => 1,
                'sms_status' => 1,
                'push_status' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove notification templates for driver subscriptions
        DB::table('notification_templates')->whereIn('act', [
            'DRIVER_SUBSCRIPTION_PAYMENT',
            'DRIVER_SUBSCRIPTION_EXPIRING',
            'DRIVER_SUBSCRIPTION_FAILED',
        ])->delete();
    }
};
