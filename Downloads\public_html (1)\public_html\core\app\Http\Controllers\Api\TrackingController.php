<?php

namespace App\Http\Controllers\Api;

use App\Constants\Status;
use App\Http\Controllers\Controller;
use App\Models\RideTrackingCode;
use Illuminate\Http\Request;

class TrackingController extends Controller
{
    /**
     * Track a ride using a tracking code (public endpoint, no auth required)
     */
    public function trackRideByCode($code)
    {
        // Find the tracking code
        $trackingCode = RideTrackingCode::where('tracking_code', $code)
            ->active()
            ->first();

        if (!$trackingCode) {
            $notify[] = 'Invalid or expired tracking code';
            return apiResponse('invalid_code', 'error', $notify);
        }

        // Get the ride
        $ride = $trackingCode->ride;

        // Check if the ride is still active or running
        if (!in_array($ride->status, [Status::RIDE_ACTIVE, Status::RIDE_RUNNING])) {
            $notify[] = 'This ride is no longer active';
            return apiResponse('inactive_ride', 'error', $notify);
        }

        // Load necessary relationships
        $ride->load('driver', 'service');

        // Get the driver's current location
        $driver = $ride->driver;

        $notify[] = 'Ride tracking information retrieved successfully';
        return apiResponse('tracking_info', 'success', $notify, [
            'ride' => [
                'id' => $ride->id,
                'uid' => $ride->uid,
                'status' => $ride->status,
                'pickup_location' => $ride->pickup_location,
                'destination' => $ride->destination,
                'driver' => [
                    'name' => $driver->fullname,
                    'phone' => $driver->mobileNumber,
                    'image' => $driver->imageSrc,
                    'service' => $ride->service->name,
                ],
                'tracking_expires_at' => $trackingCode->expires_at,
            ],
            'location' => [
                'latitude' => $driver->latitude,
                'longitude' => $driver->longitude,
            ],
            'pusher' => [
                'channel' => 'tracking-' . $code,
                'event' => 'location-update',
            ]
        ]);
    }
}
