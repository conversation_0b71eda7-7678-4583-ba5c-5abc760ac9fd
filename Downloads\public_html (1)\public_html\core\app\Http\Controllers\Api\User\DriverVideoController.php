<?php

namespace App\Http\Controllers\Api\User;

use App\Models\Ride;
use App\Models\DriverVideo;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;

class DriverVideoController extends Controller
{
    /**
     * Get all videos for a ride
     */
    public function getVideos($id)
    {
        $user = Auth::user();
        $ride = Ride::where('user_id', $user->id)->find($id);

        if (!$ride) {
            $notify[] = 'Invalid ride';
            return apiResponse('not_found', 'error', $notify);
        }

        $videos = DriverVideo::where('ride_id', $ride->id)
            ->orderBy('created_at', 'desc')
            ->get();

        // Mark videos as viewed
        DriverVideo::where('ride_id', $ride->id)
            ->where('is_viewed', false)
            ->update(['is_viewed' => true]);

        $notify[] = 'Driver videos retrieved successfully';
        return apiResponse('driver_videos', 'success', $notify, [
            'videos' => $videos,
            'video_path' => getFilePath('driver_video')
        ]);
    }

    /**
     * Get a specific video
     */
    public function getVideo($id)
    {
        $user = Auth::user();
        $video = DriverVideo::with('ride', 'driver')
            ->whereHas('ride', function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })
            ->find($id);

        if (!$video) {
            $notify[] = 'Invalid video';
            return apiResponse('not_found', 'error', $notify);
        }

        // Mark video as viewed
        if (!$video->is_viewed) {
            $video->is_viewed = true;
            $video->save();
        }

        $notify[] = 'Driver video retrieved successfully';
        return apiResponse('driver_video', 'success', $notify, [
            'video' => $video,
            'video_path' => getFilePath('driver_video')
        ]);
    }

    /**
     * Get unviewed video count
     */
    public function getUnviewedCount($id)
    {
        $user = Auth::user();
        $ride = Ride::where('user_id', $user->id)->find($id);

        if (!$ride) {
            $notify[] = 'Invalid ride';
            return apiResponse('not_found', 'error', $notify);
        }

        $count = DriverVideo::where('ride_id', $ride->id)
            ->where('is_viewed', false)
            ->count();

        $notify[] = 'Unviewed video count retrieved successfully';
        return apiResponse('unviewed_count', 'success', $notify, [
            'count' => $count
        ]);
    }
}
