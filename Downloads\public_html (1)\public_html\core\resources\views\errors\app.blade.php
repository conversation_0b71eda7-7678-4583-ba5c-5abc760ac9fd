<!DOCTYPE html>
<html lang="{{ config('app.locale') }}">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title> {{ gs()->siteName($exception->getStatusCode()) }}</title>
    <link rel="shortcut icon" href="{{ siteFavicon() }}" type="image/x-icon">

    <link
        href="https://fonts.googleapis.com/css2?family=Be+Vietnam+Pro:wght@300;400;500;600;700;800;900&family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap"
        rel="stylesheet">
    <script src="{{ asset('assets/admin/js/theme.js') }}"></script>
    <link rel="stylesheet" href="{{ asset('assets/global/css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/global/css/all.min.css') }}">
    <link rel="stylesheet" href="{{ asset('assets/error/css/main.css') }}">
</head>

<body>
    <div class="error-content flex-center flex-column">
        <div class="error-icon-placeholder">
            <span class="error-icon">
                <svg width="538" height="700" viewBox="0 0 538 700" fill="none"
                    xmlns="http://www.w3.org/2000/svg">
                    <g opacity="0.05" clip-path="url(#clip0_251_1553)">
                        <path
                            d="M353.279 4.82104L353.108 4.65437C349.967 1.4148 345.634 -0.397694 341.113 -0.345611C340.555 -0.371653 339.993 -0.319569 339.446 -0.173737L83.5095 -0.173737C37.5044 -0.16332 0.218099 37.1282 0.197266 83.1385L0.197266 616.335C0.218099 662.34 37.5044 699.631 83.5095 699.647H454.586C500.591 699.631 537.883 662.34 537.899 616.335V196.102C537.867 191.69 536.138 187.456 533.065 184.279L353.279 4.82104ZM357.774 56.4771L481.409 179.779H407.764C380.191 179.695 357.852 157.362 357.774 129.795V56.4771ZM454.753 666.157H83.5095C55.9367 666.079 33.6086 643.741 33.5201 616.168V82.9666C33.6086 55.399 55.9367 33.0605 83.5095 32.9824H324.451V129.795C324.467 175.794 361.759 213.086 407.764 213.107H504.576V616.168C504.628 643.73 482.315 666.121 454.753 666.157Z"
                            fill="var(--svg-color)" />
                        <path
                            d="M165.356 352.492L184.67 333.177L200.973 349.48L201.099 349.605L201.241 349.712C203.053 351.071 205.073 351.906 207.165 351.906C209.305 351.906 211.242 351.029 212.791 349.48C216.026 346.245 216.026 340.897 212.791 337.662L196.488 321.359L218.062 299.786C221.297 296.551 221.297 291.203 218.062 287.968C214.827 284.733 209.479 284.733 206.244 287.968L184.67 309.541L165.356 290.227C162.121 286.992 156.773 286.992 153.538 290.227C150.303 293.462 150.303 298.809 153.538 302.045L172.853 321.359L153.538 340.674C150.303 343.909 150.303 349.256 153.538 352.492C156.773 355.727 162.121 355.727 165.356 352.492Z"
                            fill="var(--svg-color)" stroke="var(--svg-color)" stroke-width="5" />
                        <path
                            d="M352.279 429.255L352.296 429.273L352.315 429.292C355.55 432.527 360.897 432.527 364.132 429.292C367.365 426.059 367.368 420.718 364.141 417.482C321.771 374.35 249.363 373.543 206.948 420.535C203.73 423.808 203.774 429.233 207.879 432.429C211.17 435.579 216.556 435.493 219.723 431.383C255.633 391.901 317.142 392.654 352.279 429.255Z"
                            fill="var(--svg-color)" stroke="var(--svg-color)" stroke-width="5" />
                        <path
                            d="M388.456 284.956L373.659 299.753L354.344 280.439C351.109 277.203 345.761 277.203 342.526 280.439C339.291 283.674 339.291 289.021 342.526 292.256L361.841 311.571L344.032 329.38C340.797 332.615 340.797 337.962 344.032 341.198C347.267 344.433 352.615 344.433 355.85 341.198L373.659 323.389L392.973 342.704C396.208 345.939 401.556 345.939 404.791 342.704C408.026 339.468 408.026 334.121 404.791 330.886L385.477 311.571L400.274 296.774C403.509 293.539 403.509 288.191 400.274 284.956C397.038 281.721 391.691 281.721 388.456 284.956Z"
                            fill="var(--svg-color)" stroke="var(--svg-color)" stroke-width="5" />
                    </g>
                    <defs>
                        <clipPath id="clip0_251_1553">
                            <rect width="538" height="700" fill="white" />
                        </clipPath>
                    </defs>
                </svg>
            </span>
        </div>
        <span class="error-content__text">{{ $exception->getStatusCode() }}</span>
        @yield('content')
    </div>
</body>

</html>
