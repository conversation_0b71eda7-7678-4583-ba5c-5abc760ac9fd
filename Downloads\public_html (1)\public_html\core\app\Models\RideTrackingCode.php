<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class RideTrackingCode extends Model
{
    protected $guarded = ['id'];

    protected $casts = [
        'expires_at' => 'datetime',
        'is_active' => 'boolean',
    ];

    /**
     * Get the ride that owns the tracking code.
     */
    public function ride()
    {
        return $this->belongsTo(Ride::class);
    }

    /**
     * Get the user that created the tracking code.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope a query to only include active tracking codes.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)->where('expires_at', '>', now());
    }

    /**
     * Check if the tracking code is valid.
     */
    public function isValid()
    {
        return $this->is_active && $this->expires_at > now();
    }
}
