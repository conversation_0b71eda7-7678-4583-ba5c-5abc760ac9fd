@extends('admin.layouts.app')
@section('panel')
    <div class="row">
        <div class="col-lg-12">
            <div class="card b-radius--10">
                <div class="card-body p-0">
                    <div class="table-responsive--md table-responsive">
                        <table class="table--light style--two table">
                            <thead>
                                <tr>
                                    <th>@lang('Driver')</th>
                                    <th>@lang('Amount')</th>
                                    <th>@lang('Start Date')</th>
                                    <th>@lang('End Date')</th>
                                    <th>@lang('Status')</th>
                                    <th>@lang('Action')</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($subscriptions as $subscription)
                                    <tr>
                                        <td>
                                            <span class="fw-bold">{{ @$subscription->driver->fullname }}</span>
                                            <br>
                                            <span class="small">
                                                <a href="{{ route('admin.driver.detail', $subscription->driver_id) }}">
                                                    <span>@</span>{{ @$subscription->driver->username }}
                                                </a>
                                            </span>
                                        </td>
                                        <td>{{ showAmount($subscription->amount) }} {{ gs('cur_text') }}</td>
                                        <td>{{ showDateTime($subscription->start_date) }}</td>
                                        <td>{{ showDateTime($subscription->end_date) }}</td>
                                        <td>
                                            @if($subscription->status == \App\Models\DriverSubscription::STATUS_ACTIVE && $subscription->end_date > now())
                                                <span class="badge badge--success">@lang('Active')</span>
                                            @else
                                                <span class="badge badge--danger">@lang('Expired')</span>
                                            @endif
                                        </td>
                                        <td>
                                            <a href="{{ route('admin.driver.subscriptions.edit', $subscription->id) }}" class="btn btn-sm btn-outline--primary">
                                                <i class="la la-pencil"></i> @lang('Edit')
                                            </a>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td class="text-muted text-center" colspan="100%">{{ __($emptyMessage) }}</td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
                @if ($subscriptions->hasPages())
                    <div class="card-footer py-4">
                        {{ paginateLinks($subscriptions) }}
                    </div>
                @endif
            </div>
        </div>
    </div>
@endsection

@push('breadcrumb-plugins')
    <a href="{{ route('admin.driver.subscriptions.create') }}" class="btn btn-sm btn-outline--primary">
        <i class="las la-plus"></i>@lang('Add New')
    </a>
@endpush
