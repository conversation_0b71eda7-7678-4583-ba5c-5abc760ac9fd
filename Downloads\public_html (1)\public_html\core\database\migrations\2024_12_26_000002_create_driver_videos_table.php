<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('driver_videos', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('ride_id');
            $table->unsignedInteger('driver_id');
            $table->string('video', 255);
            $table->string('thumbnail', 255)->nullable();
            $table->text('description')->nullable();
            $table->boolean('is_viewed')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('driver_videos');
    }
};
