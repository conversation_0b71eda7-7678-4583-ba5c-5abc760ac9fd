# Shared Ride Chat Feature - API Documentation

## Overview

This document provides comprehensive details about the chat functionality between riders in shared rides, including API endpoints, request/response formats, and real-time notifications using Pusher.

## Table of Contents

1. [Authentication](#authentication)
2. [Chat Endpoints](#chat-endpoints)
3. [Real-time Notifications](#real-time-notifications)
4. [Error Handling](#error-handling)
5. [Testing Guide](#testing-guide)

## Authentication

All API requests require authentication using a Bearer token obtained during login.

### Headers
```
Authorization: Bearer {your_access_token}
Content-Type: application/json
Accept: application/json
```

## Chat Endpoints

### 1. Get Chat History

Retrieves the chat history for a shared ride.

**Endpoint:** `GET /api/shared-ride/{shared_ride_id}/chat`

**Response:**
```json
{
    "status": "success",
    "message": ["Chat history retrieved successfully"],
    "data": {
        "messages": [
            {
                "id": 1,
                "shared_ride_id": 123,
                "sender_id": 45,
                "receiver_id": 46,
                "message": "Hi, I'll be at the pickup point in 5 minutes",
                "is_read": true,
                "created_at": "2025-05-19T10:30:00.000000Z",
                "sender": {
                    "id": 45,
                    "name": "John Doe",
                    "username": "johndoe",
                    "image": "path/to/image.jpg"
                }
            }
        ]
    }
}
```

### 2. Send Message

Sends a new message in the shared ride chat.

**Endpoint:** `POST /api/shared-ride/{shared_ride_id}/chat`

**Request Body:**
```json
{
    "receiver_id": 46,
    "message": "Hi, I'll be at the pickup point in 5 minutes"
}
```

**Response:**
```json
{
    "status": "success",
    "message": ["Message sent successfully"],
    "data": {
        "message": {
            "id": 1,
            "shared_ride_id": 123,
            "sender_id": 45,
            "receiver_id": 46,
            "message": "Hi, I'll be at the pickup point in 5 minutes",
            "is_read": false,
            "created_at": "2025-05-19T10:30:00.000000Z",
            "sender": {
                "id": 45,
                "name": "John Doe",
                "username": "johndoe",
                "image": "path/to/image.jpg"
            }
        }
    }
}
```

### 3. Mark Messages as Read

Marks all unread messages in a chat as read.

**Endpoint:** `PUT /api/shared-ride/{shared_ride_id}/chat/read`

**Response:**
```json
{
    "status": "success",
    "message": ["Messages marked as read"],
    "data": null
}
```

## Real-time Notifications

The chat feature uses Pusher for real-time message delivery. When a new message is sent, both users in the shared ride receive a real-time notification.

### Event Details

- **Event Name:** `new-chat-message`
- **Channel:** `shared-ride-{shared_ride_id}`
- **Data Format:**
```json
{
    "message": {
        "id": 1,
        "shared_ride_id": 123,
        "sender_id": 45,
        "receiver_id": 46,
        "message": "Hi, I'll be at the pickup point in 5 minutes",
        "is_read": false,
        "created_at": "2025-05-19T10:30:00.000000Z",
        "sender": {
            "id": 45,
            "name": "John Doe",
            "username": "johndoe",
            "image": "path/to/image.jpg"
        }
    }
}
```

### Subscribing to Chat Updates

#### JavaScript Example:
```javascript
const pusher = new Pusher('your-pusher-key', {
    cluster: 'your-cluster'
});

const channel = pusher.subscribe(`shared-ride-${sharedRideId}`);
channel.bind('new-chat-message', function(data) {
    console.log('New message:', data.message);
    // Handle the new message in your UI
});
```

## Error Handling

### Common Error Responses

1. **Unauthorized Access:**
```json
{
    "status": "error",
    "message": ["Unauthorized access to chat"],
    "data": null
}
```

2. **Invalid Shared Ride:**
```json
{
    "status": "error",
    "message": ["Shared ride not found"],
    "data": null
}
```

3. **Validation Errors:**
```json
{
    "status": "error",
    "message": ["The message field is required"],
    "data": null
}
```

## Testing Guide

### Prerequisites

1. Valid user accounts for both riders in a shared ride
2. Active shared ride
3. Access tokens for both users

### Test Cases

1. **Basic Chat Flow**
   - Login as User 1
   - Send a message
   - Login as User 2
   - Check message received
   - Reply to message
   - Verify real-time updates

2. **Message History**
   - Verify chat history shows all messages
   - Check correct ordering (newest last)
   - Verify sender information is complete

3. **Read Status**
   - Send message from User 1
   - Verify unread status
   - Login as User 2
   - Mark as read
   - Verify read status updated

4. **Error Cases**
   - Try accessing chat with invalid shared ride ID
   - Try sending empty message
   - Try accessing chat as unauthorized user

### Example Test Flow

1. **Create Test Users and Shared Ride:**
```http
POST /api/login
{
    "username": "rider1",
    "password": "password123"
}
```

2. **Send Test Message:**
```http
POST /api/shared-ride/123/chat
Authorization: Bearer {token}
{
    "receiver_id": 46,
    "message": "Test message 1"
}
```

3. **Verify Message Received:**
```http
GET /api/shared-ride/123/chat
Authorization: Bearer {token}
```

4. **Mark Messages as Read:**
```http
PUT /api/shared-ride/123/chat/read
Authorization: Bearer {token}
```

### Testing Real-time Updates

1. Open two browser windows/tabs
2. Log in as different users in each
3. Send messages from each user
4. Verify immediate updates in both windows
5. Check notification badges/counters update correctly

## Important Notes

1. Messages are persisted in the database
2. Real-time updates work only when both users are online
3. Chat history is available even when offline
4. Users can only access chats for shared rides they're part of
5. Message timestamps are in UTC
