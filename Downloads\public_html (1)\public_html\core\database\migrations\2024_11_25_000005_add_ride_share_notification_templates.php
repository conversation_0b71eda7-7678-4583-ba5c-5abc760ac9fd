<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add notification templates for ride sharing
        DB::table('notification_templates')->insert([
            [
                'act' => 'RIDE_SHARE_REQUEST',
                'name' => 'Ride Share Request',
                'subject' => 'New Ride Share Request',
                'push_title' => 'New Ride Share Request',
                'email_body' => '<p>Hello {{fullname}},</p><p>You have received a ride share request from {{username}}.</p><p>Pickup Location: {{ride_pickup}}</p><p>Destination: {{ride_destination}}</p><p>Please check your app to accept or reject this request.</p><p>Thank you,<br />{{site_name}}</p>',
                'sms_body' => 'Hello {{fullname}}, You have received a ride share request from {{username}}. Please check your app to accept or reject this request.',
                'push_body' => '{{username}} wants to share a ride with you from {{ride_pickup}} to {{ride_destination}}',
                'shortcodes' => '{"username":"Requester Username","ride_pickup":"Pickup Location","ride_destination":"Destination Location","fullname":"Recipient Fullname"}',
                'email_status' => 1,
                'sms_status' => 1,
                'push_status' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'act' => 'RIDE_SHARE_ACCEPTED',
                'name' => 'Ride Share Request Accepted',
                'subject' => 'Ride Share Request Accepted',
                'push_title' => 'Ride Share Request Accepted',
                'email_body' => '<p>Hello {{fullname}},</p><p>Your ride share request has been accepted by {{username}}.</p><p>Pickup Location: {{ride_pickup}}</p><p>Destination: {{ride_destination}}</p><p>You can now chat with your ride partner in the app.</p><p>Thank you,<br />{{site_name}}</p>',
                'sms_body' => 'Hello {{fullname}}, Your ride share request has been accepted by {{username}}. You can now chat with your ride partner in the app.',
                'push_body' => '{{username}} has accepted your ride share request',
                'shortcodes' => '{"username":"Accepter Username","ride_pickup":"Pickup Location","ride_destination":"Destination Location","fullname":"Requester Fullname"}',
                'email_status' => 1,
                'sms_status' => 1,
                'push_status' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'act' => 'RIDE_SHARE_REJECTED',
                'name' => 'Ride Share Request Rejected',
                'subject' => 'Ride Share Request Rejected',
                'push_title' => 'Ride Share Request Rejected',
                'email_body' => '<p>Hello {{fullname}},</p><p>Your ride share request has been rejected by {{username}}.</p><p>You can try finding another ride partner in the app.</p><p>Thank you,<br />{{site_name}}</p>',
                'sms_body' => 'Hello {{fullname}}, Your ride share request has been rejected by {{username}}. You can try finding another ride partner in the app.',
                'push_body' => '{{username}} has rejected your ride share request',
                'shortcodes' => '{"username":"Rejecter Username","fullname":"Requester Fullname"}',
                'email_status' => 1,
                'sms_status' => 1,
                'push_status' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'act' => 'RIDE_SHARE_MESSAGE',
                'name' => 'Ride Share Message',
                'subject' => 'New Message from Ride Share Partner',
                'push_title' => 'New Message from Ride Share Partner',
                'email_body' => '<p>Hello {{fullname}},</p><p>You have received a new message from your ride share partner {{username}}:</p><p>"{{message}}"</p><p>Please check your app to continue the conversation.</p><p>Thank you,<br />{{site_name}}</p>',
                'sms_body' => 'Hello {{fullname}}, You have received a new message from your ride share partner {{username}}: "{{message}}"',
                'push_body' => '{{username}}: {{message}}',
                'shortcodes' => '{"username":"Sender Username","message":"Message Content","fullname":"Recipient Fullname"}',
                'email_status' => 1,
                'sms_status' => 1,
                'push_status' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove notification templates for ride sharing
        DB::table('notification_templates')->whereIn('act', [
            'RIDE_SHARE_REQUEST',
            'RIDE_SHARE_ACCEPTED',
            'RIDE_SHARE_REJECTED',
            'RIDE_SHARE_MESSAGE',
        ])->delete();
    }
};
