{"Welcome Back": "Welcome Back", "Please enter your credentials to proceed to the next step.": "Please enter your credentials to proceed to the next step.", "Username": "Username", "Password": "Password", "Login": "<PERSON><PERSON>", "Forgot your password": "Forgot your password", "Verify Code": "Verify Code", "Please check your email for the verification code and enter it below": "Please check your email for the verification code and enter it below", "Verify Now": "Verify Now", "Back to login": "Back to login", "Recover Account": "Recover Account", "Please enter your email to recover account": "Please enter your email to recover account", "Email": "Email", "Submit": "Submit", "Reset Password": "Reset Password", "Please enter your new password below to secure your account": "Please enter your new password below to secure your account", "New Password": "New Password", "Confirm Password": "Confirm Password", "Brand": "Brand", "Status": "Status", "Action": "Action", "Name": "Name", "Image": "Image", "Edit Brand": "Edit Brand", "Add Brand": "Add Brand", "Coupon": "Coupon", "Minimum Amount": "Minimum Amount", "Discount": "Discount", "Date": "Date", "Total Used": "Total Used", "times": "times", "Coupon Name": "Coupon Name", "Coupon Code": "Coupon Code", "Discount Type": "Discount Type", "Amount": "Amount", "Maximum Using Time": "Maximum Using Time", "Start From": "Start From", "End At": "End At", "Description": "Description", "Edit Coupon": "Edit Coupon", "Add Coupon": "Add Coupon", "Enter fixed amount": "Enter fixed amount", "Enter percentage": "Enter percentage", "Transaction Number": "Transaction Number", "Method": "Method", "Charge": "Charge", "After Charge": "After Charge", "Rate": "Rate", "After Rate Conversion": "After Rate Conversion", "Admin Response": "Admin Response", "Driver Deposit Information": "Driver Deposit Information", "Attachment": "Attachment", "No File": "No File", "Are you sure to approve this transaction?": "Are you sure to approve this transaction?", "Approve": "Approve", "Reject": "Reject", "Reject Deposit Confirmation": "Reject Deposit Confirmation", "Are you sure to": "Are you sure to", "reject": "reject", "deposit of": "deposit of", "Reason for Rejection": "Reason for Rejection", "Driver": "Driver", "Gateway | Transaction": "Gateway | Transaction", "Initiated": "Initiated", "Conversion": "Conversion", "Google Pay": "Google Pay", "charge": "charge", "Amount with charge": "Amount with charge", "Details": "Details", "Mobile number": "Mobile number", "Country": "Country", "Current Zone": "Current Zone", "Email Verification": "Email Verification", "Mobile Verification": "Mobile Verification", "Document Verification": "Document Verification", "Vehicle Document Verification": "Vehicle Document Verification", "Financial Overview": "Financial Overview", "Balance": "Balance", "Total Payment Received": "Total Payment Received", "Total Deposit": "Total Deposit", "Total Withdrawal": "Total Withdrawal", "Full Information": "Full Information", "Update": "Update", "First Name": "First Name", "Last Name": "Last Name", "Mobile Number": "Mobile Number", "Service": "Service", "Select Service": "Select Service", "Address": "Address", "City": "City", "State": "State", "Zip/Postal": "Zip/Postal", "Vehicle Verification": "Vehicle Verification", "2FA Verification": "2FA Verification", "Driver Document": "Driver Document", "No document found": "No document found", "Are you sure to approve this document?": "Are you sure to approve this document?", "Are you sure to reject this driver document?": "Are you sure to reject this driver document?", "Vehicle Information": "Vehicle Information", "No vehicle information driver is added": "No vehicle information driver is added", "Rules": "Rules", "No rule driver is added": "No rule driver is added", "Ban Driver": "<PERSON>", "Unban Driver": "Unban Driver", "If this driver is banned, they will no longer have access to their dashboard.": "If this driver is banned, they will no longer have access to their dashboard.", "Ban reason was": "Ban reason was", "Reason": "Reason", "Are you sure to unban this driver?": "Are you sure to unban this driver?", "No": "No", "Yes": "Yes", "Add Balance": "Add Balance", "Add funds to rider accounts by entering the desired amount below": "Add funds to rider accounts by entering the desired amount below", "Enter amount": "Enter amount", "Remark": "Remark", "Enter remark": "Enter remark", "Notifications": "Notifications", "Add funds to driver accounts by entering the desired amount below": "Add funds to driver accounts by entering the desired amount below", "Subtract Balance": "Subtract Balance", "Subtract funds to driver accounts by entering the desired amount below": "Subtract funds to driver accounts by entering the desired amount below", "Email-Mobile": "Email-Mobile", "Rides": "Rides", "Joined At": "Joined At", "total": "total", "completed": "completed", "canceled": "canceled", "Sms": "Sms", "Firebase": "Firebase", "Being Sent To": "Being Sent To", "active users found to send the notification": "active users found to send the notification", "Subject": "Subject", "Subject / Title": "Subject / Title", "Image (optional)": "Image (optional)", "Supported Files": "Supported Files", "Message": "Message", "Start Form": "Start Form", "Start form user id. e.g. 1": "Start form user id. e.g. 1", "Per Batch": "<PERSON>", "How many user": "How many user", "USER": "USER", "Cooling Period": "Cooling Period", "Waiting time": "Waiting time", "SECONDS": "SECONDS", "Select Driver": "Select Driver", "Select One": "Select One", "Number Of Top Deposited Driver": "Number Of Top Deposited Driver", "Number Of Days": "Number Of Days", "Days": "Days", "second delay. Avoid closing or refreshing the browser.": "second delay. Avoid closing or refreshing the browser.", "Configure": "Configure", "Help": "Help", "Are you sure to enable this extension?": "Are you sure to enable this extension?", "Enable": "Enable", "Are you sure to disable this extension?": "Are you sure to disable this extension?", "Disable": "Disable", "Update Extension": "Update Extension", "Script": "<PERSON><PERSON><PERSON>", "Paste your script with proper key": "Paste your script with proper key", "Need Help": "Need Help", "Page Name": "Page Name", "Make Slug": "Make Slug", "Page Slug": "<PERSON>", "Page": "Page", "Below are the sections already added to this page": "Below are the sections already added to this page", "You\\'ve to click on the Update Now button to apply the changes": "You\\'ve to click on the Update Now button to apply the changes", "Drag & drop your section here": "Drag & drop your section here", "Sections": "Sections", "Drag a section to the left to display it on the page.": "Drag a section to the left to display it on the page.", "Checking": "Checking", "Available": "Available", "Slug already exists": "Slug already exists", "Slug": "Slug", "SEO Setting": "SEO Setting", "Edit": "Edit", "Are you sure to remove this page?": "Are you sure to remove this page?", "Delete": "Delete", "Add New Page": "Add <PERSON> Page", "Add New": "Add New", "The SEO settings for this page are optional. If you choose not to configure them, the global SEO settings will apply. You can adjust the global settings in": "The SEO settings for this page are optional. If you choose not to configure them, the global SEO settings will apply. You can adjust the global settings in", "SEO Image": "SEO Image", "Social Title": "Social Title", "Meta Keywords": "Meta Keywords", "Separate multiple keywords by": "Separate multiple keywords by", "comma": "comma", "or": "or", "enter": "enter", "key": "key", "Meta Description": "Meta Description", "Social Description": "Social Description", "Search section": "Search section", "Search": "Search", "SL": "SL", "Are you sure to remove this item?": "Are you sure to remove this item?", "Remove": "Remove", "Item": "<PERSON><PERSON>", "Import": "Import", "Update SEO Configuration": "Update SEO Configuration", "Ensure all SEO configurations are updated properly for optimal search engine visibility": "Ensure all SEO configurations are updated properly for optimal search engine visibility", "SELECTED": "SELECTED", "SELECT": "SELECT", "Get This": "Get This", "Global setting for": "Global setting for", "Select currency": "Select currency", "Add Currency": "Add <PERSON>cy", "Are you sure to delete this gateway currency?": "Are you sure to delete this gateway currency?", "Range": "Range", "Maximum Amount": "Maximum Amount", "Fixed Charge": "Fixed Charge", "Percent Charge": "Percent Charge", "Currency": "<PERSON><PERSON><PERSON><PERSON>", "Symbol": "Symbol", "Configuration": "Configuration", "No currency is added to this payment gateway": "No currency is added to this payment gateway", "Please select a currency": "Please select a currency", "Are you sure to enable this gateway?": "Are you sure to enable this gateway?", "Are you sure to disable this gateway?": "Are you sure to disable this gateway?", "out of": "out of", "supported currencies are currently activated for this gateway.": "supported currencies are currently activated for this gateway.", "Manual Gateway": "Manual Gateway", "Add Manual Gateway": "Add Manual Gateway", "Are you sure to enable this manual gateway?": "Are you sure to enable this manual gateway?", "Are you sure to disable this manual gateway?": "Are you sure to disable this manual gateway?", "This manual gateway currency is": "This manual gateway currency is", "Search payment gateway": "Search payment gateway", "Gateway Name": "Gateway Name", "The minimum amount must be greater than the fixed charge": "The minimum amount must be greater than the fixed charge", "The maximum amount must be greater than the minimum amount": "The maximum amount must be greater than the minimum amount", "Deposit Instruction": "Deposit Instruction", "You\\'ve to click on the submit button to apply the changes": "You\\'ve to click on the submit button to apply the changes", "Are you sure to remove this language keyword?": "Are you sure to remove this language keyword?", "See More": "See More", "Back to Language": "Back to Language", "Add New Keyword": "Add New Keyword", "Key": "Key", "Value": "Value", "Import Keywords": "Import Keywords", "Import From": "Import From", "System": "System", "Add Keyword": "Add Keyword", "Edit Keyword": "Edit Keyword", "See Less": "See Less", "Translate": "Translate", "Are you sure to remove this language from this system?": "Are you sure to remove this language from this system?", "Add New Language": "Add New Language", "Language Flag": "Language Flag", "Language Name": "Language Name", "Language Code": "Language Code", "Language Info": "Language Info", "Default Language": "Default Language", "Language Keywords": "Language Keywords", "Copy": "Copy", "Edit Language": "Edit Language", "No data found": "No data found", "There are no available data to display.": "There are no available data to display.", "Email Send Method": "Email Send Method", "PHP Mail": "PHP Mail", "SMTP": "SMTP", "SendGrid API": "SendGrid API", "Mailjet API": "Mailjet API", "SMTP Configuration": "SMTP Configuration", "Host": "Host", "smtp.googlemail.com": "smtp.googlemail.com", "Port": "Port", "Available port": "Available port", "Encryption": "Encryption", "SSL": "SSL", "TLS": "TLS", "Normally your email": "Normally your email", "Normally your email password": "Normally your email password", "SendGrid API Configuration": "SendGrid API Configuration", "App Key": "App Key", "SendGrid App key": "SendGrid App key", "Mailjet API Configuration": "Mailjet API Configuration", "Api Public Key": "Api Public Key", "Mailjet Api Public Key": "Mailjet Api Public Key", "Api Secret Key": "Api Secret Key", "Mailjet Api Secret Key": "Mailjet Api Secret Key", "Test Mail Setup": "Test Mail Setup", "Sent to": "Sent to", "Email Address": "Email Address", "Send Test Mail": "Send Test Mail", "Email Sent From - Name": "<PERSON><PERSON> From - Name", "Email address": "Email address", "Email Sent From - Email": "<PERSON><PERSON> From - Email", "Email Body": "Email Body", "Your email template": "Your email template", "Notification Title": "Notification Title", "Push Notification Body": "Push Notification Body", "Short Code": "Short Code", "Full Name of User": "Full Name of User", "Username of User": "Username of User", "SMS Sent From": "SMS Sent From", "SMS Body": "SMS Body", "Email Template": "<PERSON>ail Te<PERSON>late", "SMS Template": "SMS Template", "Push Notification Template o send push notifications via Firebase, your system must have an SSL certificate in place for secure communication. Ensure your server is SSL-certified to ena": "Push Notification Template o send push notifications via Firebase, your system must have an SSL certificate in place for secure communication. Ensure your server is SSL-certified to ena", "API Key": "API Key", "Auth Domain": "Auth Domain", "Project Id": "Project Id", "Storage Bucket": "Storage Bucket", "Messaging Sender Id": "Messaging Sender Id", "App Id": "App Id", "Measurement Id": "Measurement Id", "Firebase Setup": "Firebase Setup", "Steps": "Steps", "Configs": "Configs", "Go to your Firebase account and select": "Go to your Firebase account and select", "Go to console": "Go to console", "in the upper-right corner of the page.": "in the upper-right corner of the page.", "Click on the": "Click on the", "Add Project": "Add Project", "button.": "button.", "Enter the project name and click on the": "Enter the project name and click on the", "Continue": "Continue", "Enable Google Analytics and click on the": "Enable Google Analytics and click on the", "Choose the default account for the Google Analytics account and click on the": "Choose the default account for the Google Analytics account and click on the", "Create Project": "Create Project", "Within your Firebase project, select the gear next to Project Overview and choose Project settings.": "Within your Firebase project, select the gear next to Project Overview and choose Project settings.", "Next, set up a web app under the General section of your project settings.": "Next, set up a web app under the General section of your project settings.", "Go to the Service accounts tab and generate a new private key.": "Go to the Service accounts tab and generate a new private key.", "A JSON file will be downloaded. Upload the downloaded file here.": "A JSON file will be downloaded. Upload the downloaded file here.", "Upload Push Notification Configuration File": "Upload Push Notification Configuration File", "File": "File", "Supported Files: .json": "Supported Files: .json", "Upload Config File": "Upload Config File", "Download File": "Download File", "Sms Send Method": "Sms Send Method", "Clickatell": "Clickatell", "Infobip": "Infobip", "Message Bird": "Message Bird", "Nexmo": "Nexmo", "Sms Broadcast": "Sms Broadcast", "Twilio": "<PERSON><PERSON><PERSON>", "Text Magic": "Text Magic", "Custom API": "Custom API", "Clickatell Configuration": "Clickatell Configuration", "Infobip Configuration": "Infobip Configuration", "Message Bird Configuration": "Message Bird Configuration", "Nexmo Configuration": "Nexmo Configuration", "API Secret": "API Secret", "Sms Broadcast Configuration": "Sms Broadcast Configuration", "Twilio Configuration": "<PERSON><PERSON><PERSON> Configu<PERSON>", "Account SID": "Account SID", "Auth Token": "<PERSON><PERSON>", "From Number": "From Number", "Text Magic Configuration": "Text Magic Configuration", "Apiv2 Key": "Apiv2 Key", "API URL": "API URL", "GET": "GET", "POST": "POST", "Number": "Number", "Headers": "Headers", "Add": "Add", "Headers Name": "Headers Name", "Headers Value": "Headers Value", "Body": "Body", "Body Name": "Body Name", "Body Value": "Body Value", "Test SMS Setup": "Test SMS Setup", "Mobile": "Mobile", "Send Test SMS": "Send Test SMS", "Email subject": "Email subject", "Make the field empty if you want to use global template\\'s name as email sent from name.": "Make the field empty if you want to use global template\\'s name as email sent from name.", "Make the field empty if you want to use global template\\'s email as email sent from.": "Make the field empty if you want to use global template\\'s email as email sent from.", "Your message using short-codes": "Your message using short-codes", "Edit Template": "Edit Template", "SMS": "SMS", "Push": "<PERSON><PERSON>", "Make the field empty if you want to use global template\\'s title as notification title.": "Make the field empty if you want to use global template\\'s title as notification title.", "Make the field empty if you want to use global template\\'s name as sms sent from name.": "Make the field empty if you want to use global template\\'s name as sms sent from name.", "View": "View", "Are you sure to delete the notification?": "Are you sure to delete the notification?", "Mark All as Read": "<PERSON> as <PERSON>", "Are you sure to delete all notifications?": "Are you sure to delete all notifications?", "Delete all Notification": "Delete all Notification", "Go to Website": "Go to Website", "Quick Link": "Quick Link", "Pending Deposit": "Pending Deposit", "Pending Withdrawals": "Pending Withdrawals", "Pending Ticket": "Pending Ticket", "General Setting": "General Setting", "System Configuration": "System Configuration", "Notification Setting": "Notification Setting", "All Rider": "All Rider", "Active Rider": "Active Rider", "Banned Rider": "Banned Rider", "Language": "Language", "Theme": "Theme", "Notification": "Notification", "New notifications": "New notifications", "Anonymous": "Anonymous", "No unread notifications were found": "No unread notifications were found", "There is no available data to display here at the moment": "There is no available data to display here at the moment", "View All Notification": "View All Notification", "Profile": "Profile", "Admin": "Admin", "My Profile": "My Profile", "Change Password": "Change Password", "Logout": "Logout", "Update your password": "Update your password", "Please ensure your new password is at least 6 characters long to maintain the security of your account.": "Please ensure your new password is at least 6 characters long to maintain the security of your account.", "Dashboard": "Dashboard", "Profile Information": "Profile Information", "View and manage your profile details including name, username, and email.": "View and manage your profile details including name, username, and email.", "Joined at": "Joined at", "Update Profile": "Update Profile", "User Type": "User Type", "All": "All", "Rider": "Rider", "Title": "Title", "Notifications will be sent using push notifications": "Notifications will be sent using push notifications", "TRX": "TRX", "Transacted": "Transacted", "Login at": "Login at", "IP": "IP", "Location": "Location", "Browser | OS": "Browser | OS", "Lookup IP": "Lookup IP", "Sent": "<PERSON><PERSON>", "Sender": "Sender", "via": "via", "N/A": "N/A", "Detail": "Detail", "Notification Details": "Notification Details", "To": "To", "Send Notification": "Send Notification", "Ride": "Ride", "Payment Type": "Payment Type", "Cash Payment": "Cash Payment", "Online Payment": "Online Payment", "Post Balance": "Post Balance", "Transaction Type": "Transaction Type", "Plus": "Plus", "Minus": "Minus", "Review From": "Review From", "Review to": "Review to", "Rating": "Rating", "View Review": "View Review", "Review": "Review", "Total Payment": "Total Payment", "Total Cash Payment": "Total Cash Payment", "Total Online Payment": "Total Online Payment", "Last Payment Amount": "Last Payment Amount", "Login History": "Login History", "View All": "View All", "Ban Rider": "Ban <PERSON>", "Unban Rider": "Unban Rider", "If this rider is banned, they will no longer have access to their dashboard.": "If this rider is banned, they will no longer have access to their dashboard.", "Are you sure to unban this rider?": "Are you sure to unban this rider?", "Notifications Logs": "Notifications Logs", "User": "User", "active riders found to send the notification": "active riders found to send the notification", "Select Rider": "Select Rider", "Number Of Top Deposited Rider": "Number Of Top Deposited Rider", "Rule Name": "Rule Name", "Add Rule": "Add Rule", "Edit Rule": "Edit Rule", "Bid Amount": "<PERSON><PERSON>", "Bid Date Time": "Bid Date Time", "Location & Distance Information": "Location & Distance Information", "Pickup Location": "Pickup Location", "Destination": "Destination", "Distance": "Distance", "KM": "KM", "Ride Start Time": "Ride Start Time", "Ride End Time": "Ride End Time", "Duration": "Duration", "Ride Information": "Ride Information", "UID": "UID", "Ride Type": "Ride Type", "Intercity Ride": "Intercity Ride", "City Ride": "City Ride", "Note": "Note", "Rider Information": "Rider Information", "Total Canceled Ride": "Total Canceled Ride", "Driver Information": "Driver Information", "Bid Information": "Bid Information", "Total Bids": "Total Bids", "Max Bid": "<PERSON>", "Min Bid": "<PERSON>", "Offer Amount": "Offer Amount", "Payment Information": "Payment Information", "Payment Status": "Payment Status", "Commission Amount": "Commission Amount", "Driver Received": "Driver Received", "Rider Review & Rating": "Rider Review & Rating", "Driver Review & Rating": "Driver Review & Rating", "Live Location": "Live Location", "Live location": "Live location", "Ride Id": "Ride Id", "City Fare": "City Fare", "Intercity Fare": "Intercity Fare", "Commission": "Commission", "Recommended": "Recommended", "Intercity": "Intercity", "Min": "Min", "Max": "Max", "Edit Service": "Edit Service", "Add Service": "Add Service", "Search configuration": "Search configuration", "GDPR Cookie Policy": "GDPR Cookie Policy", "Short Description": "Short Description", "From this page, you can add/update CSS for the user interface. Changing content on this page required programming knowledge.": "From this page, you can add/update CSS for the user interface. Changing content on this page required programming knowledge.", "Please do not change/edit/add anything without having proper knowledge of it. The website may misbehave due to any mistake you have made.": "Please do not change/edit/add anything without having proper knowledge of it. The website may misbehave due to any mistake you have made.", "Write Custom CSS": "Write Custom CSS", "Site Title": "Site Title", "Timezone": "Timezone", "Date Format": "Date Format", "Time Format": "Time Format", "Site Primary Color": "Site Primary Color", "Site Secondary Color": "Site Secondary Color", "Currency Symbol": "Currency Symbol", "Currency Showing Format": "Currency Showing Format", "Show Currency Text and Symbol Both": "Show Currency Text and Symbol Both", "Show Currency Text Only": "Show Currency Text Only", "Show Currency Symbol Only": "Show Currency Symbol Only", "Allow Precision": "Allow Precision", "Digit": "Digit", "Thousand Separator": "Thousand Separator", "Record to Display Per Page": "Record to Di<PERSON>lay Per Page", "20 items": "20 items", "50 items": "50 items", "100 items": "100 items", "Minimum Distance for Ride": "Minimum Distance for Ride", "Free Ride Cancellation for Riders": "Free Ride Cancellation for Riders", "Maximum Negative Balance of Drivers": "Maximum Negative Balance of Drivers", "MAP SETTING": "MAP SETTING", "Google Maps Api": "Google Maps Api", "Center Latitude": "Center Latitude", "Center Longitude": "Center Longitude", "PUSHER CONFIGURATION": "PUSHER CONFIGURATION", "App ID": "App ID", "App Secret": "App Secret", "Cluster f the logo and favicon do not update after changes are made on this page, please clear your browser cache. Since we retain the same filename after the update,": "Cluster f the logo and favicon do not update after changes are made on this page, please clear your browser cache. Since we retain the same filename after the update,", "Clear cache": "Clear cache", "Logo Light": "Logo Light", "Logo Dark": "Logo Dark", "Favicon": "Favicon", "Maintenance Mode Content": "Maintenance Mode Content", "Insert Robots txt": "Insert Robots txt", "Insert Sitemap XML": "Insert Sitemap XML", "Subscribe At": "Subscribe At", "Are you sure to remove this subscriber?": "Are you sure to remove this subscriber?", "Send Email": "Send Email", "How many subscriber": "How many subscriber", "Priority": "Priority", "High": "High", "Medium": "Medium", "Low": "Low", "Open": "Open", "Answer": "Answer", "Reply": "Reply", "Close": "Close", "Ticket#": "Ticket#", "Enter reply here": "Enter reply here", "You can upload up to 5 files with a maximum size of": "You can upload up to 5 files with a maximum size of", "Supported file formats include .jpg, .jpeg, .png, .pdf, .doc, and .docx.": "Supported file formats include .jpg, .jpeg, .png, .pdf, .doc, and .docx.", "Add Attachment": "Add Attachment", "Posted on": "Posted on", "Are you sure to delete this message?": "Are you sure to delete this message?", "Staff": "Staff", "Are you want to close this support ticket?": "Are you want to close this support ticket?", "Close Ticket": "Close Ticket", "Submitted By": "Submitted By", "Last Reply": "Last Reply", "Ticket": "Ticket", "Application Details": "Application Details", "Explore key details about your application, including its name, version, localization info, and more.": "Explore key details about your application, including its name, version, localization info, and more.", "Application Name": "Application Name", "Application Version": "Application Version", "OvoPanel Version": "OvoPanel Version", "Laravel Version": "Laravel Version", "Maintenance Mode": "Maintenance Mode", "Server Details": "Server Details", "Explore key details about your server, including its php version, database info, server info and more.": "Explore key details about your server, including its php version, database info, server info and more.", "PHP Version": "PHP Version", "Database": "Database", "Server Software": "Server Software", "Server IP Address": "Server IP Address", "Server Protocol": "Server Protocol", "HTTP Host": "HTTP Host", "Server Port": "Server Port", "App Information": "App Information", "Explore key details about your mobile app, including its version and more.": "Explore key details about your mobile app, including its version and more.", "App Version": "App Version", "Flutter Version": "Flutter Version", "iOS Support": "iOS Support", "Minimum Android Version": "Minimum Android Version", "Minimum Apple Version": "Minimum Apple Version", "Clear Cache": "<PERSON>ache", "If you clear the cache, your application will be optimized and ready to run smoothly.": "If you clear the cache, your application will be optimized and ready to run smoothly.", "Clear Now": "Clear Now", "Compiled views will be cleared": "Compiled views will be cleared", "Application cache will be cleared": "Application cache will be cleared", "Route cache will be cleared": "Route cache will be cleared", "Configuration cache will be cleared": "Configuration cache will be cleared", "Compiled services and packages files will be removed": "Compiled services and packages files will be removed", "Caches will be cleared": "Caches will be cleared", "Withdraw Instruction": "Withdraw Instruction", "Withdraw Via": "Withdraw Via", "Trx Number": "Trx Number", "Payable": "Payable", "Driver Withdraw Information": "Driver Withdraw Information", "Withdrawal Confirmation": "<PERSON><PERSON><PERSON> Confirmation", "Have you sent": "Have you sent", "Provide the details. eg: transaction number": "Provide the details. eg: transaction number", "Reject Withdrawal Confirmation": "Reject Withdrawal Confirmation", "Reason of Rejection": "Reason of Rejection", "Are you sure to enable this method?": "Are you sure to enable this method?", "Are you sure to disable this method?": "Are you sure to disable this method?", "This method supports": "This method supports", "currency. With a fee structure of": "currency. With a fee structure of", "The transaction limits range from": "The transaction limits range from", "to": "to", "Amount after charge": "Amount after charge", "Select Area": "Select Area", "Search Here": "Search Here", "There are no available data to display on this page at the moment.": "There are no available data to display on this page at the moment.", "User Login by Browser": "User Login by <PERSON><PERSON>er", "Transactions Report": "Transactions Report", "Daily": "Daily", "Monthly": "Monthly", "Yearly": "Yearly", "Date Range": "Date Range", "Select Date": "Select Date", "Start Date - End Date": "Start Date - End Date", "Apply": "Apply", "Manage Frontend": "Manage Frontend", "No result found": "No result found", "to select": "to select", "to navigate": "to navigate", "ESC": "ESC", "to close": "to close", "Order By": "Order By", "Latest": "Latest", "Oldest": "Oldest", "Record to Display": "Record to Di<PERSON>lay", "Items": "Items", "There are no available data to display on this table at the moment.": "There are no available data to display on this table at the moment.", "Export": "Export", "Excel": "Excel", "CSV": "CSV", "PDF": "PDF", "Print": "Print", "Filter": "Filter", "Deposit": "<PERSON><PERSON><PERSON><PERSON>", "Withdrawals": "<PERSON><PERSON><PERSON><PERSON>", "Total Deposits": "Total Deposits", "Pending Deposits": "Pending Deposits", "Rejected Deposits": "Rejected Deposits", "Deposited Charge": "Deposited Charge", "Pending Deposit Count": "Pending Deposit Count", "Rejected Deposit Count": "Rejected Deposit Count", "Pending Withdrawal": "Pending <PERSON>", "Rejected Withdrawal": "Rejected <PERSON>", "Withdrawal Charge": "Withdrawal Charge", "Pending Withdrawal Count": "Pending <PERSON><PERSON>wal Count", "Rejected Withdrawal Count": "Rejected <PERSON><PERSON><PERSON> Count", "Deposit & Withdraw Report": "Deposit & Withdraw Report", "Back": "Back", "Please Confirm!": "Please Confirm!", "Required": "Required", "Optional": "Optional", "Type:": "Type:", "Width:": "Width:", "100%": "100%", "50%": "50%", "33%": "33%", "25%": "25%", "There are no available fields to display on this form at the moment.": "There are no available fields to display on this form at the moment.", "Type": "Type", "Text": "Text", "URL": "URL", "Date & Time": "Date & Time", "Time": "Time", "Textarea": "Textarea", "Select": "Select", "Checkbox": "Checkbox", "Radio": "Radio", "Is Required": "Is Required", "Label": "Label", "Width": "<PERSON><PERSON><PERSON>", "Instruction": "Instruction", "(if any)": "(if any)", "Click to Upload": "Click to Upload", "or drag and drop here": "or drag and drop here", "Supported Files:": "Supported Files:", "Image will be resized into": "Image will be resized into", "px": "px", "Supported mimes": "Supported mimes", "Page not found": "Page not found", "The page you are looking for may not exist, or an error has occurred. It might also be temporarily unavailable.": "The page you are looking for may not exist, or an error has occurred. It might also be temporarily unavailable.", "Back to Home": "Back to Home", "Session expired": "Session expired", "Please refresh your browser and try again to continue where you left off.": "Please refresh your browser and try again to continue where you left off.", "Oops": "Oops", "Internal server error": "Internal server error", "Captcha": "<PERSON><PERSON>", "Captcha field is required.": "Captcha field is required.", "Latest Blog": "Latest Blog", "Phone": "Phone", "Office": "Office", "See on Google Map": "See on Google Map", "Your name": "Your name", "Your Email": "Your Email", "Enter your subject": "Enter your subject", "How can we help?": "How can we help?", "By contacting us, you agree to our": "By contacting us, you agree to our", "and": "and", "Authorize Net": "Authorize Net", "Name on Card": "Name on Card", "Card Number": "Card Number", "Expiration Date": "Expiration Date", "CVC Code": "CVC Code", "Checkout.com": "Checkout.com", "Payment Preview": "Payment Preview", "PLEASE SEND EXACTLY": "PLEASE SEND EXACTLY", "TO": "TO", "SCAN TO SEND": "SCAN TO SEND", "REDIRECTING TO THE APP": "REDIRECTING TO THE APP", "Thank you for your patience. Please hold on as we are redirecting you to the app shortly.": "Thank you for your patience. Please hold on as we are redirecting you to the app shortly.", "Flutterwave": "Flutterwave", "You have to pay": "You have to pay", "You will get": "You will get", "Pay Now": "Pay Now", "You are requesting": "You are requesting", "to deposit.": "to deposit.", "Please pay": "Please pay", "for successful payment.": "for successful payment.", "NMI": "NMI", "Paystack": "Paystack", "Razorpay": "Razorpay", "Stripe Hosted": "Stripe Hosted", "Stripe Storefront": "Stripe Storefront", "Deposit with Stripe": "Deposit with <PERSON><PERSON>", "Home": "Home", "This site uses cookies": "This site uses cookies", "View More": "View More", "Accept All": "Accept All", "Your email": "Your email", "Company": "Company", "Blog": "Blog", "Contact": "Contact", "Legal": "Legal", "All rights reserved.": "All rights reserved.", "Still have questions?": "Still have questions?", "Get in touch": "Get in touch", "Max 5 files can be uploaded | Maximum upload size is ' . convertToReadableSize(ini_get('upload_max_filesize": "Max 5 files can be uploaded | Maximum upload size is ' . convertToReadableSize(ini_get('upload_max_filesize", "No replies found here!": "No replies found here!", "A Better Way to Ride": "A Better Way to Ride", "Choose a service that combines reliability, safety, and comfort for every trip": "Choose a service that combines reliability, safety, and comfort for every trip", "Latest News orem ipsum dolor sit, amet consectetur adipisicing elit. Doloribus necessitatibus repudiandae porro reprehenderit, beatae perferendis repellat quo ipsa omnis,": "Latest News orem ipsum dolor sit, amet consectetur adipisicing elit. Doloribus necessitatibus repudiandae porro reprehenderit, beatae perferendis repellat quo ipsa omnis,", "Facebook": "Facebook", "Ride Smarter, Ride Better e may utilize cookies when you access our website, including any related media platforms or mobile applications. These technologies are employed to enhance si": "Ride Smarter, Ride Better e may utilize cookies when you access our website, including any related media platforms or mobile applications. These technologies are employed to enhance si", "Privacy Policy": "Privacy Policy", "Terms of Service": "Terms of Service", "Latest Newsss omplete KYC to unlock the full potential of our platform! KYC helps us verify your identity and keep things secure. It is quick and easy just follow the on-sc our KYC verification is being reviewed. We might need some additional information. You will get an email update soon. In the meantime, explore our platform wi": "Latest Newsss omplete KYC to unlock the full potential of our platform! KYC helps us verify your identity and keep things secure. It is quick and easy just follow the on-sc our KYC verification is being reviewed. We might need some additional information. You will get an email update soon. In the meantime, explore our platform wi", "We regret to inform you that the Know Your Customer (KYC) information provided has been reviewed and unfortunately, it has not met our verification standards.": "We regret to inform you that the Know Your Customer (KYC) information provided has been reviewed and unfortunately, it has not met our verification standards.", "Registration Currently Disabled": "Registration Currently Disabled", "Page you are looking for doesn't exit or an other error occurred or temporarily unavailable.": "Page you are looking for doesn't exit or an other error occurred or temporarily unavailable.", "Go to Home": "Go to Home", "Find Your Answers Fast": "Find Your Answers Fast", "Ride Your Way, Anytime, Anywhere": "Ride Your Way, Anytime, Anywhere", "Download Rider App": "Download Rider App", "Download Driver App": "Download Driver App", "Bike": "Bike", "Our Super Platform": "Our Super Platform", "Enjoy hassle-free rides with our reliable, affordable, and safe ride-sharing service.": "Enjoy hassle-free rides with our reliable, affordable, and safe ride-sharing service.", "Car": "Car", "Truck": "Truck", "Jeep": "Jeep", "How it works": "How it works", "Payment & Feedback": "Payment & Feedback", "Secure payment is processed through the app, and both parties can rate each other, enhancing trust and service quality.": "Secure payment is processed through the app, and both parties can rate each other, enhancing trust and service quality.", "Select & Ride": "Select & Ride", "Riders choose their preferred driver based on the bids received and enjoy a comfortable ride.": "Riders choose their preferred driver based on the bids received and enjoy a comfortable ride.", "Request & Bid": "Request & Bid", "Riders enter their trip details, and drivers submit competitive bids. Riders can compare offers based on price and ratings.": "Riders enter their trip details, and drivers submit competitive bids. Riders can compare offers based on price and ratings.", "Sign Up & Verify": "Sign Up & Verify", "Create an account as a rider or driver. Verification ensures a safe and reliable community.": "Create an account as a rider or driver. Verification ensures a safe and reliable community.", "Get our ride sharing app": "Get our ride sharing app", "Accurate real-time tracking": "Accurate real-time tracking", "Guaranteed affordable fixed rates": "Guaranteed affordable fixed rates", "Dynamic flexible location selection": "Dynamic flexible location selection", "What Our Riders Are Saying": "What Our Riders Are Saying", "Hear from our satisfied riders and drivers about their experiences with our ride-sharing platform!": "Hear from our satisfied riders and drivers about their experiences with our ride-sharing platform!", "Sarah Johnson": "<PERSON>", "OvoRide Rider": "OvoRide Rider", "\"Highly Recommend! Always\" used this platform for a day-long truck rental, and it was fantastic. The pricing was transparent, the truck was in great condition, and the booking process": "\"Highly Recommend! Always\" used this platform for a day-long truck rental, and it was fantastic. The pricing was transparent, the truck was in great condition, and the booking process", "Emily Chen": "<PERSON>", "\"Peaceful Real-Time Rides\" he real-time tracking feature gave me peace of mind during my rides. I could see exactly where my driver was and when to expect them, making the experience st": "\"Peaceful Real-Time Rides\" he real-time tracking feature gave me peace of mind during my rides. I could see exactly where my driver was and when to expect them, making the experience st", "David Martinez": "<PERSON>", "\"Rewards That Impress\" appreciate the referral rewards! I’ve shared this app with my friends, and we all love the convenient rides and excellent rates. The rewards system makes it": "\"Rewards That Impress\" appreciate the referral rewards! I’ve shared this app with my friends, and we all love the convenient rides and excellent rates. The rewards system makes it", "Alex Rodriguez": "<PERSON>", "\"Reliable and Enjoyable Rides\" mazing service! The app is incredibly user-friendly, and I love being able to choose from multiple drivers based on their ratings and profiles. My rides are a": "\"Reliable and Enjoyable Rides\" mazing service! The app is incredibly user-friendly, and I love being able to choose from multiple drivers based on their ratings and profiles. My rides are a", "Stories Behind The Wheel": "Stories Behind The Wheel", "CONTACT US": "CONTACT US", "Get in touch today": "Get in touch today", "Lorem ipsum dolor sit amet consectetur adipiscing elit nulla adipiscing tincidunt interdum tellus du.": "Lorem ipsum dolor sit amet consectetur adipiscing elit nulla adipiscing tincidunt interdum tellus du.", "<EMAIL>": "<EMAIL>", "794 Mcallister St San Francisco, 94102 ttps://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d7299.283753613704!2d90.42125349540545!3d23.83133057856955!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2": "794 Mcallister St San Francisco, 94102 ttps://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d7299.283753613704!2d90.42125349540545!3d23.83133057856955!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2", "Completed Rides": "Completed Rides", "Active Riders": "Active Riders", "Active Drivers": "Active Drivers", "Cancel Ratio": "Cancel Ratio", "Competitive Pricing": "Competitive Pricing", "Riders get the best deals as drivers bid to provide fair, affordable rates.": "Riders get the best deals as drivers bid to provide fair, affordable rates.", "Flexible Bidding": "Flexible Bidding", "Drivers can bid on rides, giving riders multiple options to choose from.": "Drivers can bid on rides, giving riders multiple options to choose from.", "Real-Time Updates": "Real-Time Updates", "Instant updates on bids, offers, and trip progress keep riders informed.": "Instant updates on bids, offers, and trip progress keep riders informed.", "Safety Assurance": "Safety Assurance", "Ensuring a safe, reliable, and worry-free ride experience.": "Ensuring a safe, reliable, and worry-free ride experience.", "Integrated in-app real-time chat": "Integrated in-app real-time chat", "Essay Driver ratings and reviews": "Essay Driver ratings and reviews", "Customizable Discounts and promotions": "Customizable Discounts and promotions", "The Role of Technology in Ride-Sharing": "The Role of Technology in Ride-Sharing", "Sustainability and Eco-Friendly Rides": "Sustainability and Eco-Friendly Rides", "Safety First: Enhancing Passenger Security": "Safety First: Enhancing Passenger Security", "Does OvoRide support real-time tracking?": "Does OvoRide support real-time tracking?", "Yes, OvoRide includes real-time tracking for both riders and drivers, ensuring accurate pickup and drop-off locations and enhanced user experience.": "Yes, OvoRide includes real-time tracking for both riders and drivers, ensuring accurate pickup and drop-off locations and enhanced user experience.", "What platforms are supported by OvoRide?": "What platforms are supported by OvoRide?", "OvoRide supports both Android and iOS platforms for Rider and Driver apps. The Admin Panel is accessible via a web browser": "OvoRide supports both Android and iOS platforms for Rider and Driver apps. The Admin Panel is accessible via a web browser", "Driver registrations and approvals through OvoRide?": "Driver registrations and approvals through OvoRide?", "Yes, the Admin Panel allows you to manage driver registrations, review driver details, and approve or reject drivers to maintain platform quality.": "Yes, the Admin Panel allows you to manage driver registrations, review driver details, and approve or reject drivers to maintain platform quality.", "Does OvoRide support payment integration?": "Does OvoRide support payment integration?", "OvoRide includes support for integrating popular payment gateways, enabling secure and convenient payments for both riders and drivers.": "OvoRide includes support for integrating popular payment gateways, enabling secure and convenient payments for both riders and drivers.", "Does OvoRide offer ride history for users and drivers?": "Does OvoRide offer ride history for users and drivers?", "Yes, both riders and drivers can access their ride history within the app, making it easy to track completed trips, payments, and other relevant details.": "Yes, both riders and drivers can access their ride history within the app, making it easy to track completed trips, payments, and other relevant details.", "How secure is the data in OvoRide?": "How secure is the data in OvoRide?", "OvoRide is built with strong security protocols to ensure that user data, payment information, and ride details are protected.": "OvoRide is built with strong security protocols to ensure that user data, payment information, and ride details are protected.", "Stay up to date on all the latest news": "Stay up to date on all the latest news", "OvoRide is a complete ride-sharing solution designed to simplify transportation and connect riders.": "OvoRide is a complete ride-sharing solution designed to simplify transportation and connect riders.", "Cookie Policy": "<PERSON><PERSON>", "Let’s get in touch": "Let’s get in touch", "Don’t be afraid to say hello with us!": "Don’t be afraid to say hello with us!", "Contact Us": "Contact Us", "You can reach us anytime": "You can reach us anytime", "Twitter": "Twitter", "Instant Booking": "Instant Booking", "Get a ride on demand with just a few taps. Choose your vehicle and book instantly!ew taps. Choose your vehicle and book instantly!": "Get a ride on demand with just a few taps. Choose your vehicle and book instantly!ew taps. Choose your vehicle and book instantly!", "Real Time Tracking": "Real Time Tracking", "Track your driver’s location in real-time and know exactly when your ride will arrive.eal-time and know exactly when your ride will arrive.": "Track your driver’s location in real-time and know exactly when your ride will arrive.eal-time and know exactly when your ride will arrive.", "Multiple Payment Option": "Multiple Payment Option", "Pay your way with flexible options, including cash, card, and digital wallets including cash, card, and digital wallets": "Pay your way with flexible options, including cash, card, and digital wallets including cash, card, and digital wallets", "Affordable Pricing": "Affordable Pricing", "Enjoy transparent, affordable fares with no hidden fees. Ride within your budget with no hidden fees. Ride within your budget": "Enjoy transparent, affordable fares with no hidden fees. Ride within your budget with no hidden fees. Ride within your budget", "Wide Range of Vehicles": "Wide Range of Vehicles", "Cars": "Cars", "SUV": "SUV", "Ride Made Simple": "Ride Made Simple", "Sign up & Verify": "Sign up & Verify", "Top Tips for Safe Rides in Your City": "Top Tips for Safe Rides in Your City", "Choosing the Right Vehicle": "Choosing the Right Vehicle", "City Guides: Must-Visit Spots": "City Guides: Must-Visit Spots", "Mark Stones": "<PERSON>", "\"Perfect Solution for Moving\" his platform was a lifesaver for my move! The booking process was quick and easy, and the truck I rented was in excellent condition. The pricing was transpare": "\"Perfect Solution for Moving\" his platform was a lifesaver for my move! The booking process was quick and easy, and the truck I rented was in excellent condition. The pricing was transpare", "Alixa Hales": "<PERSON><PERSON>", "\"Highly Recommended\" highly recommend this platform for anyone planning a move! Booking a truck was straightforward, the rates were reasonable, and the well-maintained vehicle ma": "\"Highly Recommended\" highly recommend this platform for anyone planning a move! Booking a truck was straightforward, the rates were reasonable, and the well-maintained vehicle ma", "Check bid rate book a ride": "Check bid rate book a ride", "User can set their bid amount for their destination, driver bid depend on user bid amount , if user satisfy they can book ride.": "User can set their bid amount for their destination, driver bid depend on user bid amount , if user satisfy they can book ride.", "City to City Ride Service": "City to City Ride Service", "A city-to-city ride service, sometimes referred to as an intercity or long-distance ride service": "A city-to-city ride service, sometimes referred to as an intercity or long-distance ride service", "Safe and Secure Rides": "Safe and Secure Rides", "Every trip is backed by safety features and driver verification, ensuring peace of mind from start to finish.": "Every trip is backed by safety features and driver verification, ensuring peace of mind from start to finish.", "Ready to GO?": "Ready to GO?", "Login to find your next ride and make every journey memorable": "Login to find your next ride and make every journey memorable", "Join the Journey": "Join the Journey", "Register to start exploring convenient rides, whenever you need them": "Register to start exploring convenient rides, whenever you need them", "Select a service": "Select a service", "Select Destination": "Select Destination", "Let's take a ride": "Let's take a ride", "File Not Found": "File Not Found", "Pickup Destination": "Pickup Destination", "Where to go?": "Where to go?", "Recommend price is {priceKey} approximately distance {distanceKey}": "Recommend price is {priceKey} approximately distance {distanceKey}", "When": "When", "SOS": "SOS", "Number of passengers": "Number of passengers", "Offer your rate": "Offer your rate", "Payment Method": "Payment Method", "Select Payment Method": "Select Payment Method", "Comment (optional)": "Comment (optional)", "Book Ride": "Book Ride", "Rides History": "Rides History", "Ride Amount": "Ride Amount", "Ride Summary": "Ride Summary", "Ride Details": "Ride Details", "Running Rides": "Running Rides", "New Rides": "New Rides", "Ride Place": "Ride Place", "Ride created": "Ride created", "Ride Completed": "Ride Completed", "Ride Cancel": "Ride Cancel", "Active Rides": "Active Rides", "Accept Ride": "Accept Ride", "Accept Request": "Accept Request", "Reject Request": "Reject Request", "Complete Rides": "Complete Rides", "Cancel Ride": "Cancel Ride", "Cancel Rides": "Cancel Rides", "Add Coupon Code": "Add Coupon Code", "Apply Coupon": "Apply Coupon", "Or": "Or", "Select Coupon": "Select Coupon", "Min spend": "Min spend", "Refer Friend": "Refer <PERSON>", "Referral a friends": "<PERSON><PERSON><PERSON> a friends", "Invite Friend & Business": "Invite Friend & Business", "Earn": "<PERSON><PERSON><PERSON>", "Invite a friend": "Invite a friend", "They register": "They register", "Get reward to complete First order.": "Get reward to complete First order.", "Inbox": "Inbox", "Write your message": "Write your message", "Account": "Account", "Miss you! Here`s": "Miss you! Here`s", "off": "off", "Get Started": "Get Started", "Next": "Next", "Skip": "<PERSON><PERSON>", "Ex on": "Ex on", "Settings & Support": "Settings & Support", "Code Applied!": "Code Applied!", "Review for Driver": "Review for Driver", "Rating driver": "Rating driver", "Rate Us": "Rate Us", "What could be better?": "What could be better?", "View Bids": "View Bids", "OTP": "OTP", "Delete Account": "Delete Account", "Withdraw Information": "Withdraw Information", "Withdraw Limit": "Withdraw Limit", "Withdraw Confirm": "Withdraw Confirm", "Authorization Method": "Authorization Method", "Please select an authorization mode": "Please select an authorization mode", "File download at": "File download at", "Find Driver": "Find Driver", "Error downloading file": "Error downloading file", "Downloading": "Downloading", "File downloaded successfully": "File downloaded successfully", "Any": "Any", "Pay": "Pay", "Sign Up": "Sign Up", "Deposit Charge": "Deposit Charge", "Final Amount": "Final Amount", "Trx No": "Trx No", "Please": "Please", "Select Remarks": "Select Remarks", "Select Trx Type": "Select Trx Type", "Before Charge": "Before Charge", "Remaining Balance": "Remaining Balance", "Enter transaction no.": "Enter transaction no.", "Latest Payments": "Latest Payments", "Remember Me": "Remember Me", "Forgot Password?": "Forgot Password?", "Sign In": "Sign In", "Last": "Last", "Back Home": "Back Home", "KYC Data": "KYC Data", "Reference Name": "Reference Name", "Reference Name(optional)": "Reference Name(optional)", "Loading": "Loading", "Already have an account?": "Already have an account?", "KYC": "KYC", "is required": "is required", "Confirm": "Confirm", "Choose File": "Choose <PERSON>", "Your KYC is under review": "Your KYC is under review", "KYC Verification Required": "KYC Verification Required", "Please submit the required KYC information to verify yourself. Otherwise, you couldn't make any ride request.": "Please submit the required KYC information to verify yourself. Otherwise, you couldn't make any ride request.", "Thank you for submitting your KYC documents. Our team is currently reviewing the information.": "Thank you for submitting your KYC documents. Our team is currently reviewing the information.", "You are already verified": "You are already verified", "Verify": "Verify", "See All": "See All", "All Ride": "All Ride", "Payment": "Payment", "Payment ID": "Payment ID", "Withdraw History": "Withdraw History", "Total Balance": "Total Balance", "TopUp": "TopUp", "Topup Wallet": "Topup Wallet", "Add Topup Amount": "Add Topup Amount", "Payable Amount": "Payable Amount", "Withdraw Money": "Withdraw Money", "Your new password must different form \\nprevious used passwords": "Your new password must different form \\nprevious used passwords", "Enter new password": "Enter new password", "Current Password": "Current Password", "Done": "Done", "Please fill up all of the fields": "Please fill up all of the fields", "Full Name": "Full Name", "Phone Number": "Phone Number", "Zip Code": "Zip Code", "Account & Settings": "Account & Settings", "Invite to signup using your link and you will get": "Invite to signup using your link and you will get", "first order": "first order", "Edit Profile": "Edit Profile", "Select Language": "Select Language", "Two Factor Authentication": "Two Factor Authentication", "Enter 6-digit code from your two factor authenticator APP.": "Enter 6-digit code from your two factor authenticator APP.", "Are you sure you want to exit the app?": "Are you sure you want to exit the app?", "Has uppercase letter": "Has uppercase letter", "Has lowercase letter": "Has lowercase letter", "Has digit": "Has digit", "Has special character": "Has special character", "Min of 6 characters": "Min of 6 characters", "Receivable": "Receivable", "Conversion Rate": "Conversion Rate", "In": "In", "Username or Email": "Username or Email", "Enter your username or email": "Enter your username or email", "Create an account": "Create an account", "Please fill out this field": "Please fill out this field", "To secure your account please provide a secure password": "To secure your account please provide a secure password", "Enter your email or username below to receive a password reset verification code": "Enter your email or username below to receive a password reset verification code", "A 6 digits verification code sent to your email address": "A 6 digits verification code sent to your email address", "Phone No.": "Phone No.", "Password reset email sent to": "Password reset email sent to", "We've sent you an access code via email for email verification": "We've sent you an access code via email for email verification", "Didn't receive code?": "Didn't receive code?", "Resend again": "Resend again", "Verify Your Email": "Verify Your Email", "A verification code has been send to": "A verification code has been send to", "Sms Verification": "Sms Verification", "Profile Complete": "Profile Complete", "Ready to Ride!": "Ready to Ride!", "Complete Your Profile & Start Your Ride": "Complete Your Profile & Start Your Ride", "Enter your": "Enter your", "Stay in City to City": "Stay in City to City", "Deposit Info": "Deposit Info", "Payment History": "Payment History", "Recent Payments": "Recent Payments", "Deposit History": "Deposit History", "Wallet": "Wallet", "My Wallet": "My Wallet", "Transaction No.": "Transaction No.", "Privacy & Policy": "Privacy & Policy", "Forget Password": "Forget Password", "Don't have an account?": "Don't have an account?", "Enter username or email": "Enter username or email", "Policies": "Policies", "Maximum 5 attachment can send": "Maximum 5 attachment can send", "Verification failed": "Verification failed", "Email verification failed": "Email verification failed", "Email verification success": "Email verification success", "Enter your password": "Enter your password", "Sorry! there are no data to show": "Sorry! there are no data to show", "Search country": "Search country", "Payment Now": "Payment Now", "I agree with the": "I agree with the", "Create new password": "Create new password", "Please provide a strong password to protect your account": "Please provide a strong password to protect your account", "Enter current password": "Enter current password", "Enter confirm password": "Enter confirm password", "Add withdraw": "Add withdraw", "Please enter an amount": "Please enter an amount", "Continue with Google": "Continue with Google", "Withdraw Method": "Withdraw Method", "Withdraw Method Edit": "Withdraw Method Edit", "Withdraw Preview": "Withdraw Preview", "Search by trx id": "Search by trx id", "No transaction found": "No transaction found", "Trx Id": "Trx Id", "Completed": "Completed", "Cancel": "Cancel", "Call": "Call", "Bank Name": "Bank Name", "Account Name": "Account Name", "Account Number": "Account Number", "Routing Number": "Routing Number", "PAY NOW": "PAY NOW", "No deposit found": "No deposit found", "No withdraw found": "No withdraw found", "OTP verification": "OTP verification", "Show more": "Show more", "More": "More", "success": "success", "Version": "Version", "Sign out successfully": "Sign out successfully", "Enter valid email": "Enter valid email", "Enter your current password": "Enter your current password", "Enter your new password": "Enter your new password", "Password must be contain 1 special character and number": "Password must be contain 1 special character and number", "Password doesn't match": "Password doesn't match", "Enter first name": "Enter first name", "Enter last name": "Enter last name", "Username must be 6 character": "Username must be 6 character", "Password Verification": "Password Verification", "Resend the code successfully": "Resend the code successfully", "Failed to resend code": "Failed to resend code", "Something went wrong": "Something went wrong", "Invalid amount": "Invalid amount", "Verification success": "Verification success", "Enter your username": "Enter your username", "Enter your email": "Enter your email", "Enter your phone number": "Enter your phone number", "Enter phone number": "Enter phone number", "000-000": "000-000", "Confirm your password": "Confirm your password", "Didn't receive the code?": "Didn't receive the code?", "We've sent you an access code to your phone number for SMS verification": "We've sent you an access code to your phone number for SMS verification", "Select a country": "Select a country", "Request failed": "Request failed", "Request success": "Request success", "Login failed,please try again": "<PERSON><PERSON> failed,please try again", "Select one": "Select one", "#ERROR!": "#ERROR!", "No internet connection": "No internet connection", "Retry": "Retry", "Otp field can't be empty": "Otp field can't be empty", "Sorry something went wrong here, go back and retry after sometimes": "Sorry something went wrong here, go back and retry after sometimes", "second ago": "second ago", "minutes ago": "minutes ago", "hour ago": "hour ago", "days ago": "days ago", "just now": "just now", "Sign Out": "Sign Out", "Menu": "<PERSON><PERSON>", "Bad response format!": "Bad response format!", "Server error": "Server error", "Unauthorized": "Unauthorized", "your email": "your email", "Password change successfully": "Password change successfully", "Topup limit": "Topup limit", "Share": "Share", "Error": "Error", "Trx Type": "Trx Type", "Approved": "Approved", "Succeed": "Succeed", "Rejected": "Rejected", "Departure Date": "Departure Date", "Departure Time": "Departure Time", "Remark Type": "Remark Type", "You must agree with our privacy & policies": "You must agree with our privacy & policies", "My location": "My location", "Location services are disabled. Please enable the services": "Location services are disabled. Please enable the services", "Location permissions are denied": "Location permissions are denied", "Location permissions are permanently denied, we cannot request permissions.": "Location permissions are permanently denied, we cannot request permissions.", "Tap on location picker": "Tap on location picker", "Search your location": "Search your location", "Your trip": "Your trip", "Select location": "Select location", "Confirm Location": "Confirm Location", "Pick Location": "Pick Location", "Cancel Reason": "Cancel Reason", "Select a valid date": "Select a valid date", "Please select a service": "Please select a service", "Please select your pickup and destination location": "Please select your pickup and destination location", "Successfully copied to clipboard": "Successfully copied to clipboard", "Ride Rules": "Ride Rules", "Select Ride Rules": "Select Ride Rules", "Selected Ride Rules": "Selected Ride Rules", "An OTP has been sent to your mobile number": "An OTP has been sent to your mobile number", "Please Enter Valid OTP Code": "Please Enter Valid OTP Code", "Try After {sec} Second": "Try After {sec} Second", "Review for user": "Review for user", "Rating User": "Rating User", "Verify Your Phone Number": "Verify Your Phone Number", "Code has been send to": "Code has been send to", "write your journey experience": "write your journey experience", "Ratting": "Ratting", "Pending Rides": "Pending Rides", "km": "km", "View map": "View map", "Bid now": "Bid now", "Bid List": "Bid List", "Kindly provide the following information to complete your vehicle verification process. Your cooperation ensures a safe and reliable ride-sharing experience for both you and your passengers. Thank you!": "Kindly provide the following information to complete your vehicle verification process. Your cooperation ensures a safe and reliable ride-sharing experience for both you and your passengers. Thank you!", "No ride found in your area": "No ride found in your area", "No ride found": "No ride found", "No active ride found": "No active ride found", "No running ride found": "No running ride found", "No completed ride found": "No completed ride found", "Sorry there is no new ride found": "Sorry there is no new ride found", "Sorry there is no complete ride found": "Sorry there is no complete ride found", "Sorry there is no cancel ride found": "Sorry there is no cancel ride found", "Sorry there is no active ride found": "Sorry there is no active ride found", "Sorry there is no pending ride found": "Sorry there is no pending ride found", "Please enter minimum": "Please enter minimum", "End Ride": "End Ride", "Payment received": "Payment received", "Distance away": "Distance away", "Estimated duration": "Estimated duration", "Ride fare": "Ride fare", "Invite to sign up using your link and you will get": "Invite to sign up using your link and you will get", "Your references": "Your references", "Cash payment": "Cash payment", "Cash": "Cash", "Online": "Online", "Pay from wallet": "Pay from wallet", "Please upload your app on play store": "Please upload your app on play store", "Person": "Person", "Referred": "Referred", "Inter City Rides": "Inter City Rides", "Gateway": "Gateway", "Searching": "Searching", "No referral users": "No referral users", "Please accept driver before call": "Please accept driver before call", "Please accept driver before chat": "Please accept driver before chat", "How many people will be going?": "How many people will be going?", "Enter coupon Code": "Enter coupon Code", "You don't have any ride yet.": "You don't have any ride yet.", "Ride rules": "Ride rules", "Something went wrong while Taking Location": "Something went wrong while Taking Location", "Create a request": "Create a request", "Miss you! here`s": "Miss you! here`s", "No service available": "No service available", "Additional information": "Additional information", "Write your additional information": "Write your additional information", "Write something here...": "Write something here...", "Finding drivers": "Finding drivers", "FAQ": "FAQ", "No file chosen": "No file chosen", "Your Message": "Your Message", "Ride Canceled": "Ride Canceled", "Wait for driver response": "Wait for driver response", "Ticket Details": "Ticket Details", "Your Reply": "Your Reply", "Attachments": "Attachments", "Answered": "Answered", "Replied": "Replied", "Closed": "Closed", "Create Ticket": "Create Ticket", "Type here": "Type here", "Enter your message": "Enter your message", "Subject is required": "Subject is required", "Message is required": "Message is required", "Support Ticket": "Support Ticket", "Our community group": "Our community group", "Complaint Box": "Complaint Box", "Customer Reply": "Customer Reply", "Replied successfully": "Replied successfully", "No Ticket Found": "No Ticket Found", "No Message Found": "No Message Found", "You": "You", "Message can't be empty": "Message can't be empty", "Ticket created successfully": "Ticket created successfully", "Are you sure you want to close the ticket": "Are you sure you want to close the ticket", "Enter file": "Enter file", "upload": "upload", "Supported file type:jpg,xlxs,pdf": "Supported file type:jpg,xlxs,pdf", "Are you sure want to\\nreject?": "Are you sure want to\\nreject?", "Request from": "Request from", "Successfully Sent Money Request": "Successfully Sent Money Request", "Enter Your Massage": "Enter Your Massage", "User Massage": "User Massage", "Successfully image downloaded": "Successfully image downloaded", "Are you sure you want to close this ticket": "Are you sure you want to close this ticket", "Pending": "Pending", "Active": "Active", "Running": "Running", "Canceled": "Canceled", "Paid": "Paid", "Unpaid": "Unpaid", "Complete Profile": "Complete Profile", "Accept": "Accept", "Decline": "Decline", "Hide": "<PERSON>de", "Show": "Show", "We're Down For Maintenance": "We're Down For Maintenance", "Application is now undergoing maintenance and will be back soon": "Application is now undergoing maintenance and will be back soon", "Check Bid Rate & Book a Ride": "Check Bid Rate & Book a Ride", "Intercity Service": "Intercity Service", "A city-to-city ride service, sometimes referred to as an intercity or long-distance ride service.": "A city-to-city ride service, sometimes referred to as an intercity or long-distance ride service.", "Search Country": "Search Country", "Search Your Zone": "Search Your Zone", "Are you sure": "Are you sure", "You want to exit the app?": "You want to exit the app?", "Faq": "Faq", "Welcome to OvorRide Driver": "Welcome to OvorR<PERSON> Driver", "Please Enter your credential": "Please Enter your credential", "Create New Account": "Create New Account", "Register with Google": "Register with Google", "Create your account to start your trip": "Create your account to start your trip", "Select Brand": "Select Brand", "Enter Location": "Enter Location", "Vehicle information": "Vehicle information", "Number Of Passengers": "Number Of Passengers", "Your offer amount": "Your offer amount", "Comment (Optional)": "Comment (Optional)", "New Ride": "New Ride", "Accepted Ride": "Accepted Ride", "Ride in Progress": "Ride in Progress", "Booking Summary": "Booking Summary", "Ride Placed": "Ride Placed", "Active Ride": "Active Ride", "Complete Ride": "Complete Ride", "Running Ride": "Running Ride", "Check your running ride": "Check your running ride", "Complete your active ride and start new ride. Before complete running ride you can not start new ride": "Complete your active ride and start new ride. Before complete running ride you can not start new ride", "OR": "OR", "Offline": "Offline", "Online Registration": "Online Registration", "Driver profile Information": "Driver profile Information", "Vehicle Number": "Vehicle Number", "Registration Date": "Registration Date", "Select vehicle name": "Select vehicle name", "Select vehicle color": "Select vehicle color", "How many seats": "How many seats", "Referral a driver": "Referral a driver", "Pickup Passenger": "Pickup Passenger", "Settings": "Settings", "Review for Customer": "Review for Customer", "Rating customer": "Rating customer", "Send OTP": "Send OTP", "Delete Your Account": "Delete Your Account", "You will lose all of your data by deleting your account. This action cannot be undone.": "You will lose all of your data by deleting your account. This action cannot be undone.", "Type your password": "Type your password", "Account deleted successfully": "Account deleted successfully", "Please enter your password": "Please enter your password", "Latest Transactions": "Latest Transactions", "Country Name": "Country Name", "Your driver verification is under review": "Your driver verification is under review", "Your vehicle verification is under review": "Your vehicle verification is under review", "Transaction": "Transaction", "Transaction ID": "Transaction ID", "Withdraw": "Withdraw", "Top up Wallet": "Top up Wallet", "Add Top up Amount": "Add Top up Amount", "Your new password must different form": "Your new password must different form", "previous used passwords": "previous used passwords", "Send bid on {symbol}{money}": "Send bid on {symbol}{money}", "Inter City": "Inter City", "Resend Code": "Resend Code", "Transactions": "Transactions", "Recent Transactions": "Recent Transactions", "History": "History", "Transaction History": "Transaction History", "Verification Failed": "Verification Failed", "Email Verification Failed": "Email Verification Failed", "Email Verification Success": "Email Verification Success", "Payment Pending": "Payment Pending", "Add Withdraw": "Add Withdraw", "Enter Amount": "Enter Amount", "Google": "Google", "No Transaction Found": "No Transaction Found", "Ride UID": "Ride UID", "No Deposit Found": "No Deposit Found", "No Withdraw Found": "No Withdraw Found", "OTP Verification": "OTP Verification", "OTP Verify from customer": "OTP Verify from customer", "Show More": "Show More", "Sign Out Successfully": "Sign Out Successfully", "Verification Success": "Verification Success", "Enter your first name": "Enter your first name", "Enter your last name": "Enter your last name", "Select your zone": "Select your zone", "Request Failed": "Request Failed", "Request Success": "Request Success", "Bad Response Format!": "Bad Response Format!", "Server Error": "Server Error", "Deposit Limit": "Deposit Limit", "My Location": "My Location", "Tap On Location Picker": "Tap On Location Picker", "Search Your Location": "Search Your Location", "Your Trip": "Your Trip", "Select Location": "Select Location", "Confirm Destination": "Confirm Destination", "Driver Verification Required": "Driver Verification Required", "Please submit the required KYC information to verify yourself. Otherwise, you couldn't make any withdrawal request.": "Please submit the required KYC information to verify yourself. Otherwise, you couldn't make any withdrawal request.", "Thank you for submitting your driver documents. Our team is currently reviewing the information.": "Thank you for submitting your driver documents. Our team is currently reviewing the information.", "Review For User": "Review For User", "Ready to Trip": "Ready to Trip", "Profile complete: step into the future": "Profile complete: step into the future", "View Map": "View Map", "Please enable your location permission": "Please enable your location permission", "BID NOW": "BID NOW", "No Ride Found in Your Area": "No Ride Found in Your Area", "Payment Received": "Payment Received", "Distance Away": "Distance Away", "Estimated Duration": "Estimated Duration", "Ride Fare": "Ride Fare", "Wait for user payment": "Wait for user payment", "Your References": "Your References", "Driver Document Verification": "Driver Document Verification", "Please Verify Your Vehicle": "Please Verify Your Vehicle", "Please verify your vehicle and start ride, earn more ride more": "Please verify your vehicle and start ride, earn more ride more", "Wait until admin approved your verification request": "Wait until admin approved your verification request", "Refered": "Refered", "Sorry! there are no referral driver found": "Sorry! there are no referral driver found", "Please select maximum 5 items": "Please select maximum 5 items", "Reply ticket can't be empty": "Reply ticket can't be empty", "Permission denied": "Permission denied", "Download directory not found": "Download directory not found", "No doc openner apps": "No doc openner apps", "file Not Found": "file Not Found", "Sorry! there are no ticket to show": "Sorry! there are no ticket to show", "No support ticket found": "No support ticket found", "Choose a file": "Choose a file", "Supported File Type:": "Supported File Type:", ".jpg, .jpeg, .png, .pdf, .doc, .docx": ".jpg, .jpeg, .png, .pdf, .doc, .docx", "Upload": "Upload", "You want to close this ticket?": "You want to close this ticket?", "Setup Key": "Setup Key", "Copied to your clipboard!": "Copied to your clipboard!", "Download": "Download", "Google Authenticator is a multifactor app for mobile devices. It generates timed codes used during the 2-step verification process. To use Google Authenticator, install the Google Authenticator application on your mobile device.": "Google Authenticator is a multifactor app for mobile devices. It generates timed codes used during the 2-step verification process. To use Google Authenticator, install the Google Authenticator application on your mobile device.", "Disable 2FA Security": "Disable 2FA Security", "Use the QR code or setup key on your Google Authenticator app to add your account.": "Use the QR code or setup key on your Google Authenticator app to add your account.", "Manage your 2FA security": "Manage your 2FA security", "Add Your Account": "Add Your Account", "Enable 2FA Security": "Enable 2FA Security", "Selected Service": "Selected Service", "Selected Brand": "Selected Brand", "Service Name": "Service Name", "Brand Name": "Brand Name"}