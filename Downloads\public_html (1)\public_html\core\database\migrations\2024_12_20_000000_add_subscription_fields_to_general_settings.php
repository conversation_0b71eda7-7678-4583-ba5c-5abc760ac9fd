<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('general_settings', function (Blueprint $table) {
            $table->decimal('driver_subscription_fee', 28, 8)->default(8500.00000000)->after('negative_balance_driver');
            $table->tinyInteger('revenue_model')->default(1)->after('driver_subscription_fee')
                ->comment('1=commission, 2=subscription');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('general_settings', function (Blueprint $table) {
            $table->dropColumn('driver_subscription_fee');
            $table->dropColumn('revenue_model');
        });
    }
};
