<?php

namespace App\Http\Controllers\Api\Driver;

use App\Events\Ride as EventsRide;
use App\Models\Ride;
use App\Models\DriverVideo;
use Illuminate\Http\Request;
use App\Rules\FileTypeValidate;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class DriverVideoController extends Controller
{
    /**
     * Upload a video for a ride
     */
    public function uploadVideo(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'video' => ['required', 'file', new FileTypeValidate(['mp4', 'mov', '3gp', 'avi'])],
            'description' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return apiResponse("validation_error", "error", $validator->errors()->all());
        }

        $driver = Auth::guard('driver')->user();
        $ride = Ride::where('driver_id', $driver->id)->find($id);

        if (!$ride) {
            $notify[] = 'Invalid ride';
            return apiResponse('not_found', 'error', $notify);
        }

        $driverVideo = new DriverVideo();
        $driverVideo->ride_id = $ride->id;
        $driverVideo->driver_id = $driver->id;
        $driverVideo->description = $request->description;

        if ($request->hasFile('video')) {
            try {
                $driverVideo->video = fileUploader($request->video, getFilePath('driver_video'), null, null);
                
                // Generate a thumbnail for the video (optional)
                // This would require FFmpeg or another video processing library
                // For now, we'll just use a placeholder
                $driverVideo->thumbnail = null;
            } catch (\Exception $exp) {
                $notify[] = "Couldn't upload your video";
                return apiResponse('exception', 'error', $notify);
            }
        }

        $driverVideo->save();

        // Send real-time notification
        initializePusher();
        $data['driver_video'] = $driverVideo->load('driver');
        event(new EventsRide($ride, "driver-video-uploaded", $data));

        $notify[] = 'Video uploaded successfully';
        return apiResponse('video_uploaded', 'success', $notify, [
            'driver_video' => $driverVideo,
            'video_path' => getFilePath('driver_video')
        ]);
    }

    /**
     * Get all videos for a ride
     */
    public function getVideos($id)
    {
        $driver = Auth::guard('driver')->user();
        $ride = Ride::where('driver_id', $driver->id)->find($id);

        if (!$ride) {
            $notify[] = 'Invalid ride';
            return apiResponse('not_found', 'error', $notify);
        }

        $videos = DriverVideo::where('ride_id', $ride->id)
            ->orderBy('created_at', 'desc')
            ->get();

        $notify[] = 'Driver videos retrieved successfully';
        return apiResponse('driver_videos', 'success', $notify, [
            'videos' => $videos,
            'video_path' => getFilePath('driver_video')
        ]);
    }

    /**
     * Delete a video
     */
    public function deleteVideo($id)
    {
        $driver = Auth::guard('driver')->user();
        $video = DriverVideo::where('driver_id', $driver->id)->find($id);

        if (!$video) {
            $notify[] = 'Invalid video';
            return apiResponse('not_found', 'error', $notify);
        }

        // Delete the video file
        if ($video->video) {
            $videoPath = getFilePath('driver_video') . '/' . $video->video;
            if (file_exists($videoPath)) {
                @unlink($videoPath);
            }
        }

        // Delete the thumbnail if exists
        if ($video->thumbnail) {
            $thumbnailPath = getFilePath('driver_video') . '/' . $video->thumbnail;
            if (file_exists($thumbnailPath)) {
                @unlink($thumbnailPath);
            }
        }

        $video->delete();

        $notify[] = 'Video deleted successfully';
        return apiResponse('video_deleted', 'success', $notify);
    }
}
