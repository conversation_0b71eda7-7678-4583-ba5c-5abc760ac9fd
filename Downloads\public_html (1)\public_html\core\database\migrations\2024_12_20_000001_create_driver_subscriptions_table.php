<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('driver_subscriptions', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('driver_id');
            $table->decimal('amount', 28, 8)->default(0);
            $table->timestamp('start_date');
            $table->timestamp('end_date');
            $table->tinyInteger('status')->default(1)->comment('1=active, 0=expired');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('driver_subscriptions');
    }
};
