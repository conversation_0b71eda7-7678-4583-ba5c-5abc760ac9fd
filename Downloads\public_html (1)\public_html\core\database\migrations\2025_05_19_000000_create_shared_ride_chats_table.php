<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSharedRideChatsTable extends Migration
{
    public function up()
    {
        Schema::create('shared_ride_chats', function (Blueprint $table) {
            $table->id();
            $table->foreignId('shared_ride_id')->constrained('shared_rides')->onDelete('cascade');
            $table->foreignId('sender_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('receiver_id')->constrained('users')->onDelete('cascade');
            $table->text('message');
            $table->boolean('is_read')->default(false);
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('shared_ride_chats');
    }
}
