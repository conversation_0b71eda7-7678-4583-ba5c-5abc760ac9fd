<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;

class DriverVideo extends Model
{
    protected $guarded = ['id'];

    protected $casts = [
        'is_viewed' => 'boolean',
    ];

    /**
     * Get the ride that owns the video.
     */
    public function ride()
    {
        return $this->belongsTo(Ride::class);
    }

    /**
     * Get the driver that owns the video.
     */
    public function driver()
    {
        return $this->belongsTo(Driver::class);
    }

    /**
     * Get the video URL.
     */
    public function videoUrl(): Attribute
    {
        return new Attribute(
            get: fn() => getFilePath('driver_video') . '/' . $this->video,
        );
    }

    /**
     * Get the thumbnail URL.
     */
    public function thumbnailUrl(): Attribute
    {
        return new Attribute(
            get: fn() => $this->thumbnail ? getFilePath('driver_video') . '/' . $this->thumbnail : null,
        );
    }
}
