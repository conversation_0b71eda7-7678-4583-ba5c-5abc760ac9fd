<?php

namespace App\Http\Controllers\Api\Driver;

use App\Constants\Status;
use App\Events\Ride as EventsRide;
use App\Http\Controllers\Controller;
use App\Models\Driver;
use App\Models\Ride;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class LocationController extends Controller
{
    /**
     * Update the driver's current location
     */
    public function updateLocation(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
        ]);

        if ($validator->fails()) {
            return apiResponse("validation_error", 'error', $validator->errors()->all());
        }

        $driver = Auth::user();
        
        // Update driver's location
        $driver->latitude = $request->latitude;
        $driver->longitude = $request->longitude;
        $driver->save();

        // Check if driver has any active rides
        $activeRides = Ride::where('driver_id', $driver->id)
            ->whereIn('status', [Status::RIDE_ACTIVE, Status::RIDE_RUNNING])
            ->where('tracking_enabled', true)
            ->get();

        // If there are active rides with tracking enabled, send location updates via Pusher
        if ($activeRides->count() > 0) {
            initializePusher();
            
            foreach ($activeRides as $ride) {
                event(new EventsRide($ride, 'driver_location', [
                    'latitude' => $request->latitude,
                    'longitude' => $request->longitude,
                ]));
            }
        }

        $notify[] = 'Location updated successfully';
        return apiResponse('location_updated', 'success', $notify);
    }

    /**
     * Get active rides with tracking enabled
     */
    public function getActiveTrackedRides()
    {
        $driver = Auth::user();
        
        $activeRides = Ride::with(['user', 'service'])
            ->where('driver_id', $driver->id)
            ->whereIn('status', [Status::RIDE_ACTIVE, Status::RIDE_RUNNING])
            ->where('tracking_enabled', true)
            ->get();

        $notify[] = 'Active tracked rides retrieved';
        return apiResponse('active_tracked_rides', 'success', $notify, [
            'rides' => $activeRides
        ]);
    }
}
