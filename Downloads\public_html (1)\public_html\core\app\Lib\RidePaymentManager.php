<?php

namespace App\Lib;

use App\Constants\Status;
use App\Models\GeneralSetting;
use App\Models\RidePayment;
use App\Models\Transaction;

class RidePaymentManager
{
    public function payment($ride, $paymentType, $customAmount = null)
    {
        $amount = $customAmount ?? ($ride->amount - $ride->discount_amount);
        $driver = $ride->driver;
        $user   = $ride->user;

        // For both gateway and wallet payments, we need to credit the driver
        if ($paymentType == Status::PAYMENT_TYPE_GATEWAY || $paymentType == Status::PAYMENT_TYPE_WALLET) {
            // For gateway payments, the user's balance deduction is handled in the RideController
            // For wallet payments, the user's balance is already deducted in the RideController

            // Only create a transaction record for gateway payments since wallet payments already have one
            if ($paymentType == Status::PAYMENT_TYPE_GATEWAY) {
                #$user->balance -= $amount;
                #$user->save();

                $transaction               = new Transaction();
                $transaction->user_id      = $user->id;
                $transaction->amount       = $amount;
                $transaction->post_balance = $user->balance;
                $transaction->charge       = 0;
                $transaction->trx_type     = '-';
                $transaction->trx          = $ride->uid;
                $transaction->remark       = $ride->is_shared ? 'shared_ride_payment' : 'payment';
                $transaction->details      = ($ride->is_shared ? 'Shared ride payment ' : 'Ride payment ') . showAmount($amount) . ' and ride uid ' . $ride->uid . '';
                $transaction->save();
            }

            // Credit the driver's account
            $driver->balance += $amount;
            $driver->save();

            $transaction               = new Transaction();
            $transaction->driver_id    = $driver->id;
            $transaction->amount       = $amount;
            $transaction->post_balance = $driver->balance;
            $transaction->charge       = 0;
            $transaction->trx_type     = '+';
            $transaction->trx          = $ride->uid;
            $transaction->remark       = $ride->is_shared ? 'shared_ride_payment_received' : 'payment_received';
            $transaction->details      = ($ride->is_shared ? 'Shared ride payment received ' : 'Ride payment received ') . showAmount($amount) . ' and ride uid ' . $ride->uid . '';
            $transaction->save();
        }

        $this->ridePayment($ride, $paymentType, $amount);

        // Check if we're using commission or subscription model
        $general = gs();

        // Only deduct commission if we're using the commission model
        if ($general->revenue_model == Status::REVENUE_COMMISSION) {
            // Calculate commission based on the actual payment amount
            $commissionPercentage = $ride->commission_percentage;
            $commissionAmount = $amount * ($commissionPercentage / 100);

            // Update the ride's commission amount if this is a shared ride
            if ($ride->is_shared && $customAmount) {
                $ride->commission_amount = $commissionAmount;
                $ride->save();
            }

            $driver->balance -= $commissionAmount;
            $driver->save();

            $transaction               = new Transaction();
            $transaction->driver_id    = $driver->id;
            $transaction->amount       = $commissionAmount;
            $transaction->post_balance = $driver->balance;
            $transaction->charge       = 0;
            $transaction->trx_type     = '-';
            $transaction->trx          = $ride->uid;
            $transaction->remark       = $ride->is_shared ? 'shared_ride_commission' : 'ride_commission';
            $transaction->details      = 'Subtract ' . ($ride->is_shared ? 'shared ' : '') . 'ride commission amount ' . showAmount($commissionAmount) . ' and ride uid ' . $ride->uid . '';
            $transaction->save();
        }
    }

    public function ridePayment($ride, $paymentType, $amount = null)
    {
        $payment               = new RidePayment();
        $payment->ride_id      = $ride->id;
        $payment->rider_id     = $ride->user_id;
        $payment->driver_id    = $ride->driver_id;
        $payment->amount       = $amount ?? ($ride->amount - $ride->discount_amount);
        $payment->payment_type = $paymentType;
        $payment->save();

        $ride->payment_status = Status::PAID;
        $ride->payment_type   = $paymentType;
        $ride->status         = Status::RIDE_COMPLETED;
        $ride->save();
    }
}
