<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('rides', function (Blueprint $table) {
            $table->boolean('is_scheduled')->default(false)->after('is_shared');
            $table->timestamp('scheduled_at')->nullable()->after('is_scheduled');
            $table->tinyInteger('schedule_status')->default(0)->after('scheduled_at')
                ->comment('0=pending, 1=notified_60min, 2=notified_30min, 3=notified_25min, 4=notified_10min, 5=notified_5min, 6=notified_1min, 7=completed');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('rides', function (Blueprint $table) {
            $table->dropColumn('is_scheduled');
            $table->dropColumn('scheduled_at');
            $table->dropColumn('schedule_status');
        });
    }
};
