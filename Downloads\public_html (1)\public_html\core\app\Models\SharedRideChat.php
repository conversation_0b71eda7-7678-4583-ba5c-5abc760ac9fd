<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SharedRideChat extends Model
{
    protected $fillable = [
        'shared_ride_id',
        'sender_id',
        'receiver_id',
        'message',
        'is_read'
    ];

    protected $casts = [
        'is_read' => 'boolean',
    ];

    public function sharedRide()
    {
        return $this->belongsTo(SharedRide::class);
    }

    public function sender()
    {
        return $this->belongsTo(User::class, 'sender_id');
    }

    public function receiver()
    {
        return $this->belongsTo(User::class, 'receiver_id');
    }
}
