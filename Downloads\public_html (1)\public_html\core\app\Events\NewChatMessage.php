<?php

namespace App\Events;

use App\Models\SharedRideChat;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class NewChatMessage implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $chat;

    public function __construct(SharedRideChat $chat)
    {
        $this->chat = $chat;
    }

    public function broadcastOn()
    {
        return [
            new PrivateChannel('user-' . $this->chat->sender_id),
            new PrivateChannel('user-' . $this->chat->receiver_id)
        ];
    }

    public function broadcastAs()
    {
        return 'new-chat-message';
    }

    public function broadcastWith()
    {
        return [
            'chat' => $this->chat->load('sender', 'receiver'),
            'user_image_path' => getFilePath('user')
        ];
    }
}
