<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('ride_share_requests', function (Blueprint $table) {
            $table->unsignedBigInteger('recipient_ride_id')->after('ride_id')->nullable();
            $table->foreign('recipient_ride_id')->references('id')->on('rides');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ride_share_requests', function (Blueprint $table) {
            $table->dropForeign(['recipient_ride_id']);
            $table->dropColumn('recipient_ride_id');
        });
    }
};
