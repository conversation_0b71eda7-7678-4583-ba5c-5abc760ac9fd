<?php

namespace App\Http\Controllers;

use App\Constants\Status;
use App\Lib\CurlRequest;
use App\Models\CronJob;
use App\Models\CronJobLog;
use App\Models\DriverSubscription;
use App\Models\GeneralSetting;
use App\Models\Ride;
use App\Models\Driver;
use App\Models\Transaction;
use Carbon\Carbon;
use Exception;
use App\Events\NewRide;
use App\Events\Ride as EventsRide;

class CronController extends Controller
{
    public function cron()
    {
        $general            = gs();
        $general->last_cron = now();
        $general->save();

        $crons = CronJob::with('schedule');

        if (request()->alias) {
            $crons->where('alias', request()->alias);
        } else {
            $crons->where('next_run', '<', now())->where('is_running', Status::YES);
        }

        $crons = $crons->get();

        foreach ($crons as $cron) {
            $cronLog              = new CronJobLog();
            $cronLog->cron_job_id = $cron->id;
            $cronLog->start_at    = now();
            if ($cron->is_default) {
                $controller = new $cron->action[0];
                try {
                    $method = $cron->action[1];
                    $controller->$method();
                } catch (\Exception $e) {
                    $cronLog->error = $e->getMessage();
                }
            } else {
                try {
                    CurlRequest::curlContent($cron->url);
                } catch (\Exception $e) {
                    $cronLog->error = $e->getMessage();
                }
            }
            $cron->last_run = now();
            $cron->next_run = now()->addSeconds($cron->schedule->interval);
            $cron->save();

            $cronLog->end_at = $cron->last_run;

            $startTime         = Carbon::parse($cronLog->start_at);
            $endTime           = Carbon::parse($cronLog->end_at);
            $diffInSeconds     = $startTime->diffInSeconds($endTime);
            $cronLog->duration = $diffInSeconds;
            $cronLog->save();
        }

        if (request()->target == 'all') {
            $notify[] = ['success', 'Cron executed successfully'];
            return back()->withNotify($notify);
        }

        if (request()->alias) {
            $notify[] = ['success', keyToTitle(request()->alias) . ' executed successfully'];
            return back()->withNotify($notify);
        }
    }

    public function cancelRide(): void
    {
        try {
            $rideCancelMinute = gs('ride_cancel_time');
            if (!$rideCancelMinute || $rideCancelMinute <= 0) return;
            $cancelTime = now()->subMinutes($rideCancelMinute);
            Ride::pending()->where('created_at', "<", $cancelTime)->update(['status' => Status::RIDE_CANCELED]);
        } catch (Exception $ex) {
            throw new Exception($ex->getMessage());
        }
    }

    public function scheduledRideNotifications(): void
    {
        try {
            // Get all scheduled rides that are pending and scheduled in the future
            $scheduledRides = Ride::where('is_scheduled', true)
                ->whereIn('status', [Status::RIDE_PENDING, Status::RIDE_ACTIVE])
                ->where('scheduled_at', '>', now())
                ->get();

            foreach ($scheduledRides as $ride) {
                $timeToRide = now()->diffInMinutes($ride->scheduled_at, false);

                // 60 minutes notification
                if ($timeToRide <= 60 && $timeToRide > 30 && $ride->schedule_status == 0) {
                    $this->sendScheduledRideNotification($ride, 1, '60 minutes');
                }

                // 30 minutes notification
                else if ($timeToRide <= 30 && $timeToRide > 25 && $ride->schedule_status <= 1) {
                    $this->sendScheduledRideNotification($ride, 2, '30 minutes');
                }

                // 25 minutes notification
                else if ($timeToRide <= 25 && $timeToRide > 10 && $ride->schedule_status <= 2) {
                    $this->sendScheduledRideNotification($ride, 3, '25 minutes');
                }

                // 10 minutes notification
                else if ($timeToRide <= 10 && $timeToRide > 5 && $ride->schedule_status <= 3) {
                    $this->sendScheduledRideNotification($ride, 4, '10 minutes');
                }

                // 5 minutes notification
                else if ($timeToRide <= 5 && $timeToRide > 1 && $ride->schedule_status <= 4) {
                    $this->sendScheduledRideNotification($ride, 5, '5 minutes');
                }

                // 1 minute notification
                else if ($timeToRide <= 1 && $timeToRide > 0 && $ride->schedule_status <= 5) {
                    $this->sendScheduledRideNotification($ride, 6, '1 minute');
                }
            }
        } catch (Exception $ex) {
            throw new Exception($ex->getMessage());
        }
    }

    private function sendScheduledRideNotification($ride, $statusCode, $timeText): void
    {
        // Update the ride schedule status
        $ride->schedule_status = $statusCode;
        $ride->save();

        // Load relationships
        $ride->load('user', 'service', 'driver');

        // Prepare notification data
        $shortCode = [
            'ride_id'         => $ride->uid,
            'service'         => $ride->service->name,
            'pickup_location' => $ride->pickup_location,
            'destination'     => $ride->destination,
            'duration'        => $ride->duration,
            'distance'        => $ride->distance,
            'scheduled_time'  => showDateTime($ride->scheduled_at),
            'time_remaining'  => $timeText
        ];

        // Initialize Pusher for real-time notifications
        initializePusher();

        // Send notification to user
        notify($ride->user, 'SCHEDULED_RIDE_REMINDER', $shortCode);

        // Send Pusher event to user
        event(new NewRide("user-".$ride->user_id, [
            'ride'              => $ride,
            'notification_type' => 'scheduled_ride_reminder',
            'time_remaining'    => $timeText,
            'user_image_path'   => getFilePath('user'),
        ]));

        // If a driver has accepted the ride, notify them too
        if ($ride->driver) {
            notify($ride->driver, 'SCHEDULED_RIDE_REMINDER', $shortCode);

            // Send Pusher event to driver
            event(new NewRide("new-ride-for-driver-".$ride->driver_id, [
                'ride'              => $ride,
                'notification_type' => 'scheduled_ride_reminder',
                'time_remaining'    => $timeText,
                'driver_image_path' => getFilePath('driver'),
                'user_image_path'   => getFilePath('user'),
            ]));
        }
    }

    public function processDriverSubscriptions(): void
    {
        try {
            $general = gs();

            // Only process if we're using the subscription model
            if ($general->revenue_model != Status::REVENUE_SUBSCRIPTION) {
                return;
            }

            // Get subscription fee amount
            $subscriptionFee = $general->driver_subscription_fee;

            // Process expired subscriptions
            $this->processExpiredSubscriptions();

            // Process subscription renewals
            $this->processSubscriptionRenewals($subscriptionFee);

            // Process subscription expiry notifications
            $this->processSubscriptionExpiryNotifications($subscriptionFee);

        } catch (Exception $ex) {
            throw new Exception($ex->getMessage());
        }
    }

    private function processExpiredSubscriptions(): void
    {
        // Mark expired subscriptions as inactive
        DriverSubscription::where('status', DriverSubscription::STATUS_ACTIVE)
            ->where('end_date', '<', Carbon::now())
            ->update(['status' => DriverSubscription::STATUS_EXPIRED]);
    }

    private function processSubscriptionRenewals($subscriptionFee): void
    {
        // Get all active drivers with expired subscriptions
        $drivers = Driver::whereHas('subscriptions', function($query) {
                $query->where('status', DriverSubscription::STATUS_ACTIVE)
                    ->where('end_date', '<', Carbon::now());
            })
            ->orWhereDoesntHave('subscriptions')
            ->where('status', Status::USER_ACTIVE)
            ->where('balance', '>=', $subscriptionFee)
            ->get();

        foreach ($drivers as $driver) {
            // Deduct subscription fee
            $driver->balance -= $subscriptionFee;
            $driver->save();

            // Create transaction record
            $transaction = new Transaction();
            $transaction->driver_id = $driver->id;
            $transaction->amount = $subscriptionFee;
            $transaction->post_balance = $driver->balance;
            $transaction->charge = 0;
            $transaction->trx_type = '-';
            $transaction->trx = getTrx();
            $transaction->remark = 'subscription_payment';
            $transaction->details = 'Weekly subscription payment';
            $transaction->save();

            // Create new subscription record
            $startDate = Carbon::now();
            $endDate = Carbon::now()->addDays(7);

            $subscription = new DriverSubscription();
            $subscription->driver_id = $driver->id;
            $subscription->amount = $subscriptionFee;
            $subscription->start_date = $startDate;
            $subscription->end_date = $endDate;
            $subscription->status = DriverSubscription::STATUS_ACTIVE;
            $subscription->save();

            // Send notification to driver
            $driver->notify('DRIVER_SUBSCRIPTION_PAYMENT', [
                'amount' => showAmount($subscriptionFee),
                'end_date' => showDateTime($endDate)
            ]);
        }
    }

    private function processSubscriptionExpiryNotifications($subscriptionFee): void
    {
        // Get drivers with subscriptions expiring in the next 24 hours
        $expiringSubscriptions = DriverSubscription::with('driver')
            ->where('status', DriverSubscription::STATUS_ACTIVE)
            ->whereBetween('end_date', [Carbon::now(), Carbon::now()->addHours(24)])
            ->get();

        foreach ($expiringSubscriptions as $subscription) {
            $driver = $subscription->driver;

            // Check if driver has sufficient balance for renewal
            if ($driver->balance < $subscriptionFee) {
                // Send insufficient balance notification
                $driver->notify('DRIVER_SUBSCRIPTION_FAILED', [
                    'amount' => showAmount($subscriptionFee),
                    'balance' => showAmount($driver->balance)
                ]);
            } else {
                // Send expiry notification
                $driver->notify('DRIVER_SUBSCRIPTION_EXPIRING', [
                    'amount' => showAmount($subscriptionFee),
                    'end_date' => showDateTime($subscription->end_date)
                ]);
            }
        }
    }
}
