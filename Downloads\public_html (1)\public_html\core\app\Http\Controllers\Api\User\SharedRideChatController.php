<?php

namespace App\Http\Controllers\Api\User;

use App\Http\Controllers\Controller;
use App\Models\SharedRide;
use App\Models\SharedRideChat;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Events\NewChatMessage;

class SharedRideChatController extends Controller
{
    public function sendMessage(Request $request, $sharedRideId)
    {
        $validator = \Illuminate\Support\Facades\Validator::make($request->all(), [
            'message' => 'required|string|max:500',
            'receiver_id' => 'required|exists:users,id'
        ]);

        if ($validator->fails()) {
            return apiResponse('validation_error', 'error', $validator->errors()->all());
        }

        $sharedRide = SharedRide::with(['primaryRide.user', 'secondaryRide.user'])
            ->where(function ($query) {
                $query->whereHas('primaryRide', function ($q) {
                    $q->where('user_id', Auth::id());
                })->orWhereHas('secondaryRide', function ($q) {
                    $q->where('user_id', Auth::id());
                });
            })
            ->find($sharedRideId);

        if (!$sharedRide) {
            $notify[] = 'Shared ride not found or you are not a participant';
            return apiResponse('not_found', 'error', $notify);
        }

        // Create the chat message
        $chat = new SharedRideChat();
        $chat->shared_ride_id = $sharedRide->id;
        $chat->sender_id = Auth::id();
        $chat->receiver_id = $request->receiver_id;
        $chat->message = $request->message;
        $chat->save();

        // Load relationships for the response
        $chat->load('sender', 'receiver');

        // Initialize Pusher for real-time notifications
        initializePusher();

        // Emit event for real-time update
        event(new NewChatMessage($chat));

        return apiResponse('message_sent', 'success', ['Message sent successfully'], [
            'chat' => $chat,
            'user_image_path' => getFilePath('user')
        ]);
    }

    public function getMessages($sharedRideId)
    {
        $sharedRide = SharedRide::with(['primaryRide.user', 'secondaryRide.user'])
            ->where(function ($query) {
                $query->whereHas('primaryRide', function ($q) {
                    $q->where('user_id', Auth::id());
                })->orWhereHas('secondaryRide', function ($q) {
                    $q->where('user_id', Auth::id());
                });
            })
            ->find($sharedRideId);

        if (!$sharedRide) {
            $notify[] = 'Shared ride not found or you are not a participant';
            return apiResponse('not_found', 'error', $notify);
        }

        // Get all messages for this shared ride
        $messages = SharedRideChat::with(['sender', 'receiver'])
            ->where('shared_ride_id', $sharedRide->id)
            ->where(function ($query) {
                $query->where('sender_id', Auth::id())
                    ->orWhere('receiver_id', Auth::id());
            })
            ->orderBy('created_at', 'asc')
            ->get();

        // Mark received messages as read
        SharedRideChat::where('shared_ride_id', $sharedRide->id)
            ->where('receiver_id', Auth::id())
            ->where('is_read', false)
            ->update(['is_read' => true]);

        return apiResponse('messages', 'success', ['Chat messages retrieved'], [
            'messages' => $messages,
            'user_image_path' => getFilePath('user')
        ]);
    }

    public function markAsRead($messageId)
    {
        $message = SharedRideChat::where('receiver_id', Auth::id())->find($messageId);

        if (!$message) {
            $notify[] = 'Message not found';
            return apiResponse('not_found', 'error', $notify);
        }

        $message->is_read = true;
        $message->save();

        return apiResponse('message_read', 'success', ['Message marked as read']);
    }

    public function getUnreadCount()
    {
        $count = SharedRideChat::where('receiver_id', Auth::id())
            ->where('is_read', false)
            ->count();

        return apiResponse('unread_count', 'success', ['Unread message count retrieved'], [
            'count' => $count
        ]);
    }
}
