<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add notification templates for scheduled rides
        DB::table('notification_templates')->insert([
            [
                'act' => 'SCHEDULED_RIDE_CREATED',
                'name' => 'Scheduled Ride Created',
                'subject' => 'Your Ride Has Been Scheduled',
                'push_title' => 'Ride Scheduled Successfully',
                'email_body' => '<p>Hello {{fullname}},</p><p>Your ride has been scheduled successfully.</p><p>Ride ID: {{ride_id}}</p><p>Service: {{service}}</p><p>Pickup Location: {{pickup_location}}</p><p>Destination: {{destination}}</p><p>Scheduled Time: {{scheduled_time}}</p><p>Thank you for using our service.</p><p>{{site_name}}</p>',
                'sms_body' => 'Hello {{fullname}}, Your ride has been scheduled for {{scheduled_time}}. Ride ID: {{ride_id}}',
                'push_body' => 'Your ride has been scheduled for {{scheduled_time}}',
                'shortcodes' => '{"ride_id":"Ride ID","service":"Service Name","pickup_location":"Pickup Location","destination":"Destination","scheduled_time":"Scheduled Time","fullname":"User Fullname"}',
                'email_status' => 1,
                'sms_status' => 1,
                'push_status' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'act' => 'SCHEDULED_RIDE_REMINDER',
                'name' => 'Scheduled Ride Reminder',
                'subject' => 'Reminder: Your Scheduled Ride',
                'push_title' => 'Upcoming Ride Reminder',
                'email_body' => '<p>Hello {{fullname}},</p><p>This is a reminder for your upcoming scheduled ride.</p><p>Ride ID: {{ride_id}}</p><p>Service: {{service}}</p><p>Pickup Location: {{pickup_location}}</p><p>Destination: {{destination}}</p><p>Scheduled Time: {{scheduled_time}}</p><p>Time Remaining: {{time_remaining}}</p><p>Thank you for using our service.</p><p>{{site_name}}</p>',
                'sms_body' => 'Hello {{fullname}}, Reminder: Your scheduled ride (ID: {{ride_id}}) is coming up in {{time_remaining}}.',
                'push_body' => 'Your scheduled ride is coming up in {{time_remaining}}',
                'shortcodes' => '{"ride_id":"Ride ID","service":"Service Name","pickup_location":"Pickup Location","destination":"Destination","duration":"Ride Duration","distance":"Ride Distance","scheduled_time":"Scheduled Time","time_remaining":"Time Remaining","fullname":"User Fullname"}',
                'email_status' => 1,
                'sms_status' => 1,
                'push_status' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove notification templates for scheduled rides
        DB::table('notification_templates')->whereIn('act', [
            'SCHEDULED_RIDE_CREATED',
            'SCHEDULED_RIDE_REMINDER',
        ])->delete();
    }
};
