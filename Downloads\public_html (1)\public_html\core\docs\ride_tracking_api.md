# Ride Tracking API Documentation

This document outlines the API endpoints for the ride tracking feature, which allows users to generate tracking codes for their rides and share them with others to track the ride's progress in real-time.

## Overview

The ride tracking feature enables:
1. Riders to generate tracking codes for active rides
2. Anyone with the tracking code to view the real-time location of the ride
3. Real-time updates via Pusher for live tracking

## API Endpoints

### 1. Generate Tracking Code (Authenticated)

Generates a tracking code for a specific ride.

**Endpoint:** `POST /api/tracking/generate-code`

**Authentication:** Required (User token)

**Request Parameters:**
```json
{
    "ride_id": 123,
    "expires_in": 60 // Optional: Minutes until expiration (default: 60, max: 1440)
}
```

**Response:**
```json
{
    "status": "success",
    "message": "tracking_code_generated",
    "data": {
        "tracking_code": "ABC12345",
        "expires_at": "2024-12-25T15:30:00.000000Z",
        "ride": {
            "id": 123,
            "uid": "RIDE123456",
            "status": 2,
            "pickup_location": "123 Main St",
            "destination": "456 Oak Ave",
            "tracking_enabled": true,
            "tracking_code": "ABC12345"
        }
    }
}
```

### 2. Track Ride (Authenticated)

Retrieves tracking information for a ride using a tracking code.

**Endpoint:** `POST /api/tracking/track-ride`

**Authentication:** Required (User token)

**Request Parameters:**
```json
{
    "tracking_code": "ABC12345"
}
```

**Response:**
```json
{
    "status": "success",
    "message": "tracking_info",
    "data": {
        "ride": {
            "id": 123,
            "uid": "RIDE123456",
            "status": 2,
            "pickup_location": "123 Main St",
            "destination": "456 Oak Ave",
            "driver": {
                "name": "John Doe",
                "phone": "+1234567890",
                "image": "https://example.com/drivers/john.jpg",
                "service": "Standard"
            },
            "tracking_expires_at": "2024-12-25T15:30:00.000000Z"
        }
    }
}
```

### 3. Get Ride Location (Authenticated)

Retrieves the current location of a ride using a tracking code.

**Endpoint:** `POST /api/tracking/get-location`

**Authentication:** Required (User token)

**Request Parameters:**
```json
{
    "tracking_code": "ABC12345"
}
```

**Response:**
```json
{
    "status": "success",
    "message": "driver_location",
    "data": {
        "latitude": 37.7749,
        "longitude": -122.4194,
        "ride_status": 2
    }
}
```

### 4. Public Tracking Endpoint (No Authentication)

Retrieves tracking information for a ride using a tracking code without requiring authentication.

**Endpoint:** `POST /api/track/{code}`

**Authentication:** Not required

**URL Parameters:**
- `code`: The tracking code (e.g., ABC12345)

**Response:**
```json
{
    "status": "success",
    "message": "tracking_info",
    "data": {
        "ride": {
            "id": 123,
            "uid": "RIDE123456",
            "status": 2,
            "pickup_location": "123 Main St",
            "destination": "456 Oak Ave",
            "driver": {
                "name": "John Doe",
                "phone": "+1234567890",
                "image": "https://example.com/drivers/john.jpg",
                "service": "Standard"
            },
            "tracking_expires_at": "2024-12-25T15:30:00.000000Z"
        },
        "location": {
            "latitude": 37.7749,
            "longitude": -122.4194
        },
        "pusher": {
            "channel": "tracking-ABC12345",
            "event": "location-update"
        }
    }
}
```

### 5. Update Driver Location (Driver)

Updates the driver's current location and broadcasts it to any active tracked rides.

**Endpoint:** `POST /api/driver/location/update`

**Authentication:** Required (Driver token)

**Request Parameters:**
```json
{
    "latitude": 37.7749,
    "longitude": -122.4194
}
```

**Response:**
```json
{
    "status": "success",
    "message": "location_updated"
}
```

### 6. Get Active Tracked Rides (Driver)

Retrieves a list of the driver's active rides with tracking enabled.

**Endpoint:** `GET /api/driver/location/active-tracked-rides`

**Authentication:** Required (Driver token)

**Response:**
```json
{
    "status": "success",
    "message": "active_tracked_rides",
    "data": {
        "rides": [
            {
                "id": 123,
                "uid": "RIDE123456",
                "status": 2,
                "tracking_enabled": true,
                "tracking_code": "ABC12345",
                "user": {
                    "id": 456,
                    "name": "Jane Smith"
                },
                "service": {
                    "id": 1,
                    "name": "Standard"
                }
            }
        ]
    }
}
```

## Real-time Updates with Pusher

The tracking feature uses Pusher for real-time location updates.

### Channel Format

- For tracking a specific ride: `tracking-{tracking_code}`
  - Example: `tracking-ABC12345`

### Events

1. **location-update**: Sent when the driver's location is updated
   ```json
   {
       "latitude": 37.7749,
       "longitude": -122.4194,
       "timestamp": "2024-12-25T15:30:00.000000Z"
   }
   ```

### Subscribing to Channels

#### Example (JavaScript)

```javascript
const pusher = new Pusher('your-app-key', {
    cluster: 'your-cluster',
    encrypted: true
});

const channel = pusher.subscribe('tracking-ABC12345');
channel.bind('location-update', function(data) {
    // Update map with new driver location
    updateDriverMarker(data.latitude, data.longitude);
});
```

## Error Responses

### Invalid Tracking Code

```json
{
    "status": "error",
    "message": "invalid_code",
    "data": ["Invalid or expired tracking code"]
}
```

### Inactive Ride

```json
{
    "status": "error",
    "message": "inactive_ride",
    "data": ["This ride is no longer active"]
}
```

### Unauthorized

```json
{
    "status": "error",
    "message": "unauthorized",
    "data": ["You are not authorized to generate a tracking code for this ride"]
}
```
