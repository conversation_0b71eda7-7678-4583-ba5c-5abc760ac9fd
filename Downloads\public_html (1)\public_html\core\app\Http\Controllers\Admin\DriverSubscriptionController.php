<?php

namespace App\Http\Controllers\Admin;

use App\Constants\Status;
use App\Http\Controllers\Controller;
use App\Models\Driver;
use App\Models\DriverSubscription;
use App\Models\Transaction;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Schema;

class DriverSubscriptionController extends Controller
{
    public function index()
    {
        $pageTitle = 'Driver Subscriptions';
        $emptyMessage = 'No subscriptions found';

        try {
            // Check if the table exists
            if (!\Schema::hasTable('driver_subscriptions')) {
                $notify[] = ['error', 'Driver subscriptions table does not exist. Please run migrations.'];
                return back()->withNotify($notify);
            }

            $subscriptions = DriverSubscription::with('driver')
                ->orderBy('id', 'desc')
                ->paginate(getPaginate());

            return view('admin.driver_subscription.index', compact('pageTitle', 'subscriptions', 'emptyMessage'));
        } catch (\Exception $e) {
            $notify[] = ['error', 'An error occurred: ' . $e->getMessage()];
            return back()->withNotify($notify);
        }
    }

    public function active()
    {
        $pageTitle = 'Active Subscriptions';
        $emptyMessage = 'No active subscriptions found';

        try {
            // Check if the table exists
            if (!Schema::hasTable('driver_subscriptions')) {
                $notify[] = ['error', 'Driver subscriptions table does not exist. Please run migrations.'];
                return back()->withNotify($notify);
            }

            $subscriptions = DriverSubscription::with('driver')
                ->where('status', DriverSubscription::STATUS_ACTIVE)
                ->where('end_date', '>', Carbon::now())
                ->orderBy('id', 'desc')
                ->paginate(getPaginate());

            return view('admin.driver_subscription.index', compact('pageTitle', 'subscriptions', 'emptyMessage'));
        } catch (\Exception $e) {
            $notify[] = ['error', 'An error occurred: ' . $e->getMessage()];
            return back()->withNotify($notify);
        }
    }

    public function expired()
    {
        $pageTitle = 'Expired Subscriptions';
        $emptyMessage = 'No expired subscriptions found';

        try {
            // Check if the table exists
            if (!Schema::hasTable('driver_subscriptions')) {
                $notify[] = ['error', 'Driver subscriptions table does not exist. Please run migrations.'];
                return back()->withNotify($notify);
            }

            $subscriptions = DriverSubscription::with('driver')
                ->where(function($query) {
                    $query->where('status', DriverSubscription::STATUS_EXPIRED)
                        ->orWhere('end_date', '<=', Carbon::now());
                })
                ->orderBy('id', 'desc')
                ->paginate(getPaginate());

            return view('admin.driver_subscription.index', compact('pageTitle', 'subscriptions', 'emptyMessage'));
        } catch (\Exception $e) {
            $notify[] = ['error', 'An error occurred: ' . $e->getMessage()];
            return back()->withNotify($notify);
        }
    }

    public function create()
    {
        $pageTitle = 'Create Subscription';

        try {
            // Check if the table exists
            if (!Schema::hasTable('driver_subscriptions')) {
                $notify[] = ['error', 'Driver subscriptions table does not exist. Please run migrations.'];
                return back()->withNotify($notify);
            }

            $general = gs();
            $drivers = Driver::where('status', Status::USER_ACTIVE)
                ->where('ev', Status::VERIFIED)
                ->where('sv', Status::VERIFIED)
                ->where('vv', Status::VERIFIED)
                ->orderBy('username')
                ->get();

            return view('admin.driver_subscription.create', compact('pageTitle', 'drivers', 'general'));
        } catch (\Exception $e) {
            $notify[] = ['error', 'An error occurred: ' . $e->getMessage()];
            return back()->withNotify($notify);
        }
    }

    public function store(Request $request)
    {
        $request->validate([
            'driver_id' => 'required|exists:drivers,id',
            'amount' => 'required|numeric|gt:0',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
        ]);

        $driver = Driver::findOrFail($request->driver_id);

        // Check if driver has sufficient balance
        if ($driver->balance < $request->amount) {
            $notify[] = ['error', 'Driver has insufficient balance for subscription'];
            return back()->withNotify($notify);
        }

        // Deduct subscription fee from driver's balance
        $driver->balance -= $request->amount;
        $driver->save();

        // Create transaction record
        $transaction = new Transaction();
        $transaction->driver_id = $driver->id;
        $transaction->amount = $request->amount;
        $transaction->post_balance = $driver->balance;
        $transaction->charge = 0;
        $transaction->trx_type = '-';
        $transaction->trx = getTrx();
        $transaction->remark = 'subscription_payment';
        $transaction->details = 'Subscription payment for period ' . showDateTime($request->start_date) . ' to ' . showDateTime($request->end_date);
        $transaction->save();

        // Create subscription record
        $subscription = new DriverSubscription();
        $subscription->driver_id = $driver->id;
        $subscription->amount = $request->amount;
        $subscription->start_date = $request->start_date;
        $subscription->end_date = $request->end_date;
        $subscription->status = DriverSubscription::STATUS_ACTIVE;
        $subscription->save();

        // Send notification to driver
        $driver->notify('DRIVER_SUBSCRIPTION_PAYMENT', [
            'amount' => showAmount($request->amount),
            'end_date' => showDateTime($request->end_date)
        ]);

        $notify[] = ['success', 'Subscription created successfully'];
        return redirect()->route('admin.driver.subscriptions.index')->withNotify($notify);
    }

    public function edit($id)
    {
        $pageTitle = 'Edit Subscription';

        try {
            // Check if the table exists
            if (!Schema::hasTable('driver_subscriptions')) {
                $notify[] = ['error', 'Driver subscriptions table does not exist. Please run migrations.'];
                return back()->withNotify($notify);
            }

            $general = gs();
            $subscription = DriverSubscription::findOrFail($id);
            $drivers = Driver::where('status', Status::USER_ACTIVE)
                ->where('ev', Status::VERIFIED)
                ->where('sv', Status::VERIFIED)
                ->where('vv', Status::VERIFIED)
                ->orderBy('username')
                ->get();

            return view('admin.driver_subscription.edit', compact('pageTitle', 'subscription', 'drivers', 'general'));
        } catch (\Exception $e) {
            $notify[] = ['error', 'An error occurred: ' . $e->getMessage()];
            return back()->withNotify($notify);
        }
    }

    public function update(Request $request, $id)
    {
        $request->validate([
            'end_date' => 'required|date',
            'status' => 'required|in:0,1',
        ]);

        $subscription = DriverSubscription::findOrFail($id);
        $subscription->end_date = $request->end_date;
        $subscription->status = $request->status;
        $subscription->save();

        $notify[] = ['success', 'Subscription updated successfully'];
        return redirect()->route('admin.driver.subscriptions.index')->withNotify($notify);
    }
}
