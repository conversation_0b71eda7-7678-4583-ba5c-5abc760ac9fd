# Driver Video API Documentation

This document outlines the API endpoints for the driver video feature, which allows drivers to send recorded videos to riders so they can see the car and the driver's environment.

## Overview

The driver video feature enhances the ride experience by:
1. Allowing drivers to record and send videos directly to riders
2. Enabling riders to visually confirm the car and driver's environment
3. Providing a more secure and transparent ride experience
4. Helping riders locate the driver more easily

## API Endpoints

### Driver Endpoints

#### 1. Upload Video

Allows drivers to upload a video for a specific ride.

**Endpoint:** `POST /api/driver/ride/upload/video/{ride_id}`

**Authentication:** Required (Driver token)

**Request Parameters:**
- `video`: Required. Video file (supported formats: mp4, mov, 3gp, avi)
- `description`: Optional. Text description of the video

**Request Example (Multipart Form Data):**
```
video: [video file]
description: "Here's my car and current location"
```

**Response:**
```json
{
    "status": "success",
    "message": "video_uploaded",
    "data": {
        "driver_video": {
            "ride_id": 123,
            "driver_id": 456,
            "description": "Here's my car and current location",
            "video": "1234567890123.mp4",
            "thumbnail": null,
            "is_viewed": false,
            "updated_at": "2024-12-26T12:34:56.000000Z",
            "created_at": "2024-12-26T12:34:56.000000Z",
            "id": 789
        },
        "video_path": "assets/videos/driver"
    }
}
```

#### 2. Get Videos

Retrieves all videos uploaded by the driver for a specific ride.

**Endpoint:** `GET /api/driver/ride/videos/{ride_id}`

**Authentication:** Required (Driver token)

**Response:**
```json
{
    "status": "success",
    "message": "driver_videos",
    "data": {
        "videos": [
            {
                "id": 789,
                "ride_id": 123,
                "driver_id": 456,
                "description": "Here's my car and current location",
                "video": "1234567890123.mp4",
                "thumbnail": null,
                "is_viewed": false,
                "created_at": "2024-12-26T12:34:56.000000Z",
                "updated_at": "2024-12-26T12:34:56.000000Z",
                "video_url": "assets/videos/driver/1234567890123.mp4",
                "thumbnail_url": null
            }
        ],
        "video_path": "assets/videos/driver"
    }
}
```

#### 3. Delete Video

Deletes a specific video uploaded by the driver.

**Endpoint:** `DELETE /api/driver/ride/video/{video_id}`

**Authentication:** Required (Driver token)

**Response:**
```json
{
    "status": "success",
    "message": "video_deleted",
    "data": ["Video deleted successfully"]
}
```

### User (Rider) Endpoints

#### 1. Get Videos

Retrieves all videos uploaded by the driver for a specific ride.

**Endpoint:** `GET /api/ride/videos/{ride_id}`

**Authentication:** Required (User token)

**Response:**
```json
{
    "status": "success",
    "message": "driver_videos",
    "data": {
        "videos": [
            {
                "id": 789,
                "ride_id": 123,
                "driver_id": 456,
                "description": "Here's my car and current location",
                "video": "1234567890123.mp4",
                "thumbnail": null,
                "is_viewed": true,
                "created_at": "2024-12-26T12:34:56.000000Z",
                "updated_at": "2024-12-26T12:35:56.000000Z",
                "video_url": "assets/videos/driver/1234567890123.mp4",
                "thumbnail_url": null
            }
        ],
        "video_path": "assets/videos/driver"
    }
}
```

#### 2. Get Specific Video

Retrieves a specific video by ID.

**Endpoint:** `GET /api/ride/video/{video_id}`

**Authentication:** Required (User token)

**Response:**
```json
{
    "status": "success",
    "message": "driver_video",
    "data": {
        "video": {
            "id": 789,
            "ride_id": 123,
            "driver_id": 456,
            "description": "Here's my car and current location",
            "video": "1234567890123.mp4",
            "thumbnail": null,
            "is_viewed": true,
            "created_at": "2024-12-26T12:34:56.000000Z",
            "updated_at": "2024-12-26T12:35:56.000000Z",
            "video_url": "assets/videos/driver/1234567890123.mp4",
            "thumbnail_url": null,
            "driver": {
                "id": 456,
                "firstname": "John",
                "lastname": "Doe",
                "image": "driver.jpg"
            },
            "ride": {
                "id": 123,
                "uid": "RIDE123456",
                "status": 2
            }
        },
        "video_path": "assets/videos/driver"
    }
}
```

#### 3. Get Unviewed Video Count

Retrieves the count of unviewed videos for a specific ride.

**Endpoint:** `GET /api/ride/unviewed-videos/{ride_id}`

**Authentication:** Required (User token)

**Response:**
```json
{
    "status": "success",
    "message": "unviewed_count",
    "data": {
        "count": 2
    }
}
```

## Real-time Notifications

The driver video feature uses Pusher for real-time notifications. When a new video is uploaded, the rider receives a real-time notification.

### Event Details

- **Event Name:** `driver-video-uploaded`
- **Channel:** `ride-{ride_id}`
- **Data Format:**
```json
{
    "driver_video": {
        "id": 789,
        "ride_id": 123,
        "driver_id": 456,
        "description": "Here's my car and current location",
        "video": "1234567890123.mp4",
        "thumbnail": null,
        "is_viewed": false,
        "created_at": "2024-12-26T12:34:56.000000Z",
        "updated_at": "2024-12-26T12:34:56.000000Z",
        "driver": {
            "id": 456,
            "firstname": "John",
            "lastname": "Doe",
            "image": "driver.jpg"
        }
    }
}
```

## Implementation Guidelines

### For Mobile App Developers

#### Recording and Uploading Videos

1. **Video Recording**:
   - Implement in-app video recording functionality
   - Limit video duration to a reasonable length (e.g., 30 seconds)
   - Compress videos to reduce file size and upload time

2. **Video Upload**:
   - Use multipart form data to upload videos
   - Show upload progress indicator
   - Handle upload failures gracefully

#### Displaying Videos

1. **Video Playback**:
   - Implement a video player component
   - Support play, pause, and seek controls
   - Add loading indicators for video buffering

2. **Notification Badges**:
   - Show notification badges for unviewed videos
   - Update badges in real-time using Pusher events

### For Drivers

1. **When to Send Videos**:
   - Send a video of your car when arriving at the pickup location
   - Send a video of your surroundings if the rider is having trouble finding you
   - Send a video of any relevant information that would help the rider

2. **Video Content Guidelines**:
   - Focus on the car and immediate surroundings
   - Keep videos short and to the point
   - Ensure good lighting and clear visibility
   - Avoid including other people or sensitive information

### For Riders

1. **Viewing Videos**:
   - Videos from drivers will appear in a dedicated section
   - Tap on a video to play it
   - Videos can help you identify the driver's car and location

## Technical Specifications

### Video Requirements

- **Supported Formats**: mp4, mov, 3gp, avi
- **Maximum File Size**: 10MB
- **Recommended Resolution**: 720p (1280x720)
- **Recommended Duration**: 10-30 seconds

## Error Handling

### Common Error Responses

#### File Too Large
```json
{
    "status": "error",
    "message": "validation_error",
    "data": ["The video may not be greater than 10MB."]
}
```

#### Unsupported File Format
```json
{
    "status": "error",
    "message": "validation_error",
    "data": ["The video file type is not supported."]
}
```

#### Upload Failed
```json
{
    "status": "error",
    "message": "exception",
    "data": ["Couldn't upload your video"]
}
```

#### Invalid Ride
```json
{
    "status": "error",
    "message": "not_found",
    "data": ["Invalid ride"]
}
```
