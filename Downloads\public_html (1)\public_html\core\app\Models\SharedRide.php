<?php

namespace App\Models;

use App\Constants\Status;
use Illuminate\Database\Eloquent\Model;

class SharedRide extends Model
{
    protected $guarded = ['id'];

    // Status constants
    const STATUS_PENDING = 0;
    const STATUS_ACCEPTED = 1;
    const STATUS_COMPLETED = 2;
    const STATUS_CANCELED = 9;

    public function primaryRide()
    {
        return $this->belongsTo(Ride::class, 'primary_ride_id');
    }

    public function secondaryRide()
    {
        return $this->belongsTo(Ride::class, 'secondary_ride_id');
    }

    public function chats()
    {
        return $this->hasMany(SharedRideChat::class);
    }

    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    public function scopeAccepted($query)
    {
        return $query->where('status', self::STATUS_ACCEPTED);
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', self::STATUS_COMPLETED);
    }

    public function scopeCanceled($query)
    {
        return $query->where('status', self::STATUS_CANCELED);
    }
}
